version: '3.8'

services:
  # 主应用服务
  remote-sensing-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: remote-sensing-api
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - MODEL_BASE_DIR=outputs
      - DEVICE=auto
      - UPLOAD_DIR=imgs
      - MAX_UPLOAD_SIZE=10485760
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=*
      - MODEL_CACHE_SIZE=1
      - API_PREFIX=/api
      - API_TITLE=遥感图像分类API
      - API_VERSION=1.0.0
      - API_DESCRIPTION=基于深度学习的遥感场景分类系统API
    volumes:
      # 挂载模型输出目录（可选，用于持久化）
      - ./outputs:/app/outputs:ro
      # 挂载图像上传目录
      - ./imgs:/app/imgs
      # 挂载日志目录
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    # GPU支持（如果需要）
    # runtime: nvidia
    # environment:
    #   - NVIDIA_VISIBLE_DEVICES=all

  # 开发模式服务（可选）
  remote-sensing-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: remote-sensing-dev
    ports:
      - "8001:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=true
      - MODEL_BASE_DIR=outputs
      - DEVICE=auto
      - LOG_LEVEL=DEBUG
    volumes:
      # 开发模式下挂载源代码
      - ./src:/app/src
      - ./outputs:/app/outputs:ro
      - ./imgs:/app/imgs
      - ./logs:/app/logs
    restart: "no"
    profiles:
      - dev
    command: ["python3", "run_api.py"]

# 网络配置
networks:
  default:
    name: remote-sensing-network

# 卷配置
volumes:
  model_cache:
    driver: local
  upload_cache:
    driver: local
