# Docker部署指南

本文档详细介绍如何使用Docker部署遥感图像分类系统。

## 📋 目录

- [快速开始](#快速开始)
- [构建选项](#构建选项)
- [运行选项](#运行选项)
- [Docker Compose](#docker-compose)
- [GPU支持](#gpu支持)
- [环境变量](#环境变量)
- [数据卷](#数据卷)
- [健康检查](#健康检查)
- [故障排除](#故障排除)

## 🚀 快速开始

### 1. 基本部署

```bash
# 构建镜像
make build

# 运行容器
make run

# 或者一键启动
make quick-start
```

### 2. 使用Docker Compose

```bash
# 启动服务
docker-compose up

# 后台启动
docker-compose up -d

# 停止服务
docker-compose down
```

### 3. 访问应用

- 主页面: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 🔧 构建选项

### 基本构建

```bash
# 使用Makefile
make build

# 使用脚本
./docker-build.sh

# 使用Docker命令
docker build -t remote-sensing-api .
```

### 高级构建选项

```bash
# 无缓存构建
make build-no-cache
./docker-build.sh --no-cache

# 指定标签
./docker-build.sh -t v1.0.0

# GPU支持构建
make gpu-build
./docker-build.sh -g

# 推送到注册表
./docker-build.sh -p -r registry.example.com
```

## 🏃 运行选项

### 基本运行

```bash
# 前台运行
make run

# 后台运行
make run-detached

# 开发模式
make dev
```

### 高级运行选项

```bash
# 指定端口
./docker-run.sh -p 8080

# GPU支持
make gpu-run
./docker-run.sh -g

# 自定义镜像
./docker-run.sh -i my-image:latest

# 挂载自定义目录
docker run -p 8000:8000 \
  -v /path/to/models:/app/outputs:ro \
  -v /path/to/uploads:/app/imgs \
  remote-sensing-api
```

## 🐳 Docker Compose

### 配置文件

项目提供了多个Docker Compose配置：

- `docker-compose.yml` - 基本配置
- `docker-compose.gpu.yml` - GPU支持配置

### 基本使用

```bash
# 启动主服务
docker-compose up

# 启动开发服务
docker-compose --profile dev up remote-sensing-dev

# GPU模式
docker-compose -f docker-compose.gpu.yml up

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 服务配置

```yaml
services:
  remote-sensing-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DEBUG=false
      - DEVICE=auto
    volumes:
      - ./outputs:/app/outputs:ro
      - ./imgs:/app/imgs
    restart: unless-stopped
```

## 🎮 GPU支持

### 前置要求

1. 安装NVIDIA Docker运行时
2. 确保主机有NVIDIA GPU
3. 安装NVIDIA驱动

### 安装NVIDIA Docker

```bash
# Ubuntu/Debian
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

### GPU运行

```bash
# 使用Makefile
make gpu-run

# 使用脚本
./docker-run.sh -g

# 使用Docker Compose
docker-compose -f docker-compose.gpu.yml up

# 直接使用Docker
docker run --gpus all -p 8000:8000 remote-sensing-api
```

## 🔧 环境变量

### 核心配置

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `HOST` | `0.0.0.0` | 服务器主机地址 |
| `PORT` | `8000` | 服务器端口 |
| `DEBUG` | `false` | 调试模式 |
| `DEVICE` | `auto` | 计算设备 (auto/cuda/cpu) |

### 模型配置

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `MODEL_BASE_DIR` | `outputs` | 模型基础目录 |
| `MODEL_CACHE_SIZE` | `1` | 模型缓存数量 |

### 上传配置

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `UPLOAD_DIR` | `imgs` | 上传目录 |
| `MAX_UPLOAD_SIZE` | `10485760` | 最大上传大小(字节) |

### 使用示例

```bash
# 环境变量文件 (.env)
HOST=0.0.0.0
PORT=8000
DEBUG=false
DEVICE=cuda
MODEL_BASE_DIR=outputs
LOG_LEVEL=INFO

# Docker运行时指定
docker run -p 8000:8000 \
  -e DEBUG=true \
  -e DEVICE=cuda \
  -e LOG_LEVEL=DEBUG \
  remote-sensing-api
```

## 💾 数据卷

### 推荐挂载点

```bash
docker run -p 8000:8000 \
  -v $(pwd)/outputs:/app/outputs:ro \    # 模型文件（只读）
  -v $(pwd)/imgs:/app/imgs \             # 上传图像
  -v $(pwd)/logs:/app/logs \             # 日志文件
  remote-sensing-api
```

### 卷说明

- `/app/outputs` - 模型文件目录，建议只读挂载
- `/app/imgs` - 图像上传目录，需要读写权限
- `/app/logs` - 日志文件目录
- `/app/src` - 源代码目录（开发模式）

## 🏥 健康检查

### 内置健康检查

容器包含自动健康检查功能：

```dockerfile
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD /app/healthcheck.sh
```

### 手动健康检查

```bash
# 检查容器健康状态
docker ps

# 查看健康检查日志
docker inspect --format='{{.State.Health}}' remote-sensing-api

# 手动执行健康检查
curl http://localhost:8000/health

# 使用Makefile
make health
```

### 健康检查响应

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

## 🔍 监控和日志

### 查看日志

```bash
# 容器日志
make logs
docker logs -f remote-sensing-api

# Docker Compose日志
make compose-logs
docker-compose logs -f

# 应用日志（如果挂载了日志目录）
tail -f logs/app.log
```

### 容器状态

```bash
# 查看容器状态
make status
docker ps -a

# 查看资源使用
docker stats remote-sensing-api

# 进入容器
make shell
docker exec -it remote-sensing-api /bin/bash
```

## 🛠️ 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看详细错误
docker logs remote-sensing-api

# 检查端口占用
netstat -tlnp | grep 8000

# 检查镜像是否存在
docker images remote-sensing-api
```

#### 2. GPU不可用

```bash
# 检查NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:11.0-base nvidia-smi

# 检查GPU设备
nvidia-smi

# 检查Docker GPU支持
docker info | grep nvidia
```

#### 3. 模型加载失败

```bash
# 检查模型目录挂载
docker exec remote-sensing-api ls -la /app/outputs

# 检查权限
docker exec remote-sensing-api ls -la /app/

# 检查环境变量
docker exec remote-sensing-api env | grep MODEL
```

#### 4. 内存不足

```bash
# 检查内存使用
docker stats remote-sensing-api

# 限制内存使用
docker run -m 4g -p 8000:8000 remote-sensing-api

# 使用Docker Compose限制
deploy:
  resources:
    limits:
      memory: 4G
```

### 调试模式

```bash
# 开启调试模式
docker run -p 8000:8000 -e DEBUG=true remote-sensing-api

# 进入容器调试
docker exec -it remote-sensing-api /bin/bash

# 查看Python进程
docker exec remote-sensing-api ps aux | grep python
```

### 性能优化

```bash
# 使用多阶段构建减少镜像大小
docker images remote-sensing-api

# 清理未使用的镜像
docker image prune

# 清理未使用的容器
docker container prune

# 清理所有未使用资源
docker system prune -a
```

## 📚 更多资源

- [Docker官方文档](https://docs.docker.com/)
- [Docker Compose文档](https://docs.docker.com/compose/)
- [NVIDIA Docker文档](https://github.com/NVIDIA/nvidia-docker)
- [项目API文档](http://localhost:8000/docs)
