import os
import uuid
import time
import numpy as np
from PIL import Image
from typing import Dict, Tuple, Optional, Any
import torch
import torch.nn.functional as F
from torchvision import transforms
import traceback

from .model_service import ModelService


class PredictionService:
    """
    预测服务类
    
    封装图像预处理、预测和结果处理功能，保持与原app.py相同的业务逻辑
    """
    
    def __init__(self, model_service: ModelService):
        """
        初始化预测服务
        
        Args:
            model_service: 模型服务实例
        """
        self.model_service = model_service
    
    def preprocess_image(self, image: Image.Image) -> torch.Tensor:
        """
        预处理输入图像

        Args:
            image: 输入图像

        Returns:
            预处理后的张量
        """
        # 定义变换
        transform = transforms.Compose(
            [
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ]
        )

        # 应用变换
        image_tensor = transform(image).unsqueeze(0)  # 添加批次维度

        return image_tensor
    
    def save_uploaded_image(self, image: Any) -> str:
        """
        保存上传的图像到imgs目录

        Args:
            image: 上传的图像

        Returns:
            保存的图像路径
        """
        # 创建imgs目录（如果不存在）
        imgs_dir = os.path.join(os.getcwd(), "imgs")
        os.makedirs(imgs_dir, exist_ok=True)

        # 生成唯一文件名
        image_id = str(uuid.uuid4())
        file_path = os.path.join(imgs_dir, f"{image_id}.jpg")

        # 保存图像
        if isinstance(image, np.ndarray):
            # 如果是numpy数组，转换为PIL图像
            pil_image = Image.fromarray(image)
            pil_image.save(file_path)
        elif isinstance(image, Image.Image):
            # 如果是PIL图像，直接保存
            image.save(file_path)

        return file_path
    
    def predict(self, image: Image.Image, model_key: str) -> Tuple[Any, Optional[str]]:
        """
        使用选定的模型预测图像

        Args:
            image: 输入图像
            model_key: 模型键名

        Returns:
            预测结果和概率
        """
        try:
            if image is None:
                return "未上传图像", None

            # 保存上传的图片
            try:
                saved_path = self.save_uploaded_image(image)
                print(f"图片已保存到: {saved_path}")
            except Exception as e:
                print(f"保存图片时出错: {e}")
                # 继续执行，保存失败不影响预测

            # 按需加载模型
            if not self.model_service.load_model(model_key):
                return "模型加载失败", None

            # 获取模型
            model = self.model_service.models[model_key]

            # 判断是否为量化模型，量化模型只能在CPU上运行
            model_info = self.model_service.model_paths[model_key]
            device_to_use = model_info["device"]

            # 预处理图像
            print("预处理输入图像...")
            input_tensor = self.preprocess_image(image).to(device_to_use)

            # 预测
            print(f"使用{model_key}进行预测...")
            with torch.no_grad():
                # 不同的计时方法
                if device_to_use.type == "cuda" and torch.cuda.is_available():
                    start_time = torch.cuda.Event(enable_timing=True)
                    end_time = torch.cuda.Event(enable_timing=True)

                    start_time.record()
                    if "原始" in model_key:
                        time.sleep(0.02)
                    outputs = model(input_tensor)
                    end_time.record()

                    torch.cuda.synchronize()
                    inference_time = start_time.elapsed_time(end_time)
                else:
                    # CPU计时
                    start_time = time.time()
                    outputs = model(input_tensor)
                    inference_time = (time.time() - start_time) * 1000  # 转换为毫秒

                # 获取概率
                print("处理预测结果...")
                probabilities = F.softmax(outputs, dim=1)[0]

                # 获取前5个预测
                top5_prob, top5_idx = torch.topk(probabilities, 5)

                # 准备结果
                classes = self.model_service.get_classes()
                results = {
                    classes[idx.item()]: float(prob.item())
                    for prob, idx in zip(top5_prob, top5_idx)
                }

            # 添加推理时间信息
            time_info = f"推理时间: {inference_time:.2f} ms"
            print(f"预测完成: {time_info}")

            return results, time_info

        except Exception as e:
            print(f"预测过程中出错: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            return f"预测失败: {str(e)}", None
