#!/bin/bash

# Docker运行脚本
# 用法: ./docker-run.sh [选项]

set -e

# 默认配置
IMAGE_NAME="remote-sensing-api:latest"
CONTAINER_NAME="remote-sensing-api"
HOST_PORT="8000"
CONTAINER_PORT="8000"
GPU_SUPPORT=false
DEV_MODE=false
DETACHED=false
REMOVE_AFTER_EXIT=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Docker运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -i, --image IMAGE       指定镜像名称 (默认: remote-sensing-api:latest)"
    echo "  -n, --name NAME         指定容器名称 (默认: remote-sensing-api)"
    echo "  -p, --port PORT         指定主机端口 (默认: 8000)"
    echo "  -g, --gpu               启用GPU支持"
    echo "  -d, --detach            后台运行"
    echo "  -r, --rm                退出后自动删除容器"
    echo "  --dev                   开发模式（挂载源代码）"
    echo "  --stop                  停止运行中的容器"
    echo "  --logs                  查看容器日志"
    echo "  --shell                 进入容器shell"
    echo ""
    echo "示例:"
    echo "  $0                      # 基本运行"
    echo "  $0 -d                   # 后台运行"
    echo "  $0 -g                   # GPU支持运行"
    echo "  $0 --dev                # 开发模式运行"
    echo "  $0 --stop               # 停止容器"
    echo "  $0 --logs               # 查看日志"
}

# 停止容器
stop_container() {
    print_message $YELLOW "停止容器: $CONTAINER_NAME"
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        docker stop $CONTAINER_NAME
        print_message $GREEN "✅ 容器已停止"
    else
        print_message $YELLOW "容器未运行"
    fi
}

# 查看日志
show_logs() {
    print_message $BLUE "显示容器日志: $CONTAINER_NAME"
    if docker ps -a -q -f name=$CONTAINER_NAME | grep -q .; then
        docker logs -f $CONTAINER_NAME
    else
        print_message $RED "容器不存在"
        exit 1
    fi
}

# 进入容器shell
enter_shell() {
    print_message $BLUE "进入容器shell: $CONTAINER_NAME"
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        docker exec -it $CONTAINER_NAME /bin/bash
    else
        print_message $RED "容器未运行"
        exit 1
    fi
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--image)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -p|--port)
            HOST_PORT="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_SUPPORT=true
            shift
            ;;
        -d|--detach)
            DETACHED=true
            shift
            ;;
        -r|--rm)
            REMOVE_AFTER_EXIT=true
            shift
            ;;
        --dev)
            DEV_MODE=true
            shift
            ;;
        --stop)
            stop_container
            exit 0
            ;;
        --logs)
            show_logs
            exit 0
            ;;
        --shell)
            enter_shell
            exit 0
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    print_message $RED "错误: Docker未安装或不可用"
    exit 1
fi

# 检查镜像是否存在
if ! docker images -q $IMAGE_NAME | grep -q .; then
    print_message $RED "错误: 镜像不存在: $IMAGE_NAME"
    print_message $YELLOW "请先构建镜像: ./docker-build.sh"
    exit 1
fi

# 停止并删除已存在的容器
if docker ps -a -q -f name=$CONTAINER_NAME | grep -q .; then
    print_message $YELLOW "删除已存在的容器: $CONTAINER_NAME"
    docker rm -f $CONTAINER_NAME
fi

# 构建运行命令
RUN_CMD="docker run"

# 添加选项
if [[ "$DETACHED" == true ]]; then
    RUN_CMD="$RUN_CMD -d"
else
    RUN_CMD="$RUN_CMD -it"
fi

if [[ "$REMOVE_AFTER_EXIT" == true ]]; then
    RUN_CMD="$RUN_CMD --rm"
fi

# 添加端口映射
RUN_CMD="$RUN_CMD -p $HOST_PORT:$CONTAINER_PORT"

# 添加容器名称
RUN_CMD="$RUN_CMD --name $CONTAINER_NAME"

# 添加环境变量
RUN_CMD="$RUN_CMD -e HOST=0.0.0.0 -e PORT=$CONTAINER_PORT"

# GPU支持
if [[ "$GPU_SUPPORT" == true ]]; then
    RUN_CMD="$RUN_CMD --gpus all"
    RUN_CMD="$RUN_CMD -e DEVICE=cuda"
    print_message $BLUE "启用GPU支持"
else
    RUN_CMD="$RUN_CMD -e DEVICE=cpu"
fi

# 开发模式
if [[ "$DEV_MODE" == true ]]; then
    RUN_CMD="$RUN_CMD -v $(pwd)/src:/app/src"
    RUN_CMD="$RUN_CMD -e DEBUG=true"
    print_message $BLUE "启用开发模式"
fi

# 添加卷挂载
RUN_CMD="$RUN_CMD -v $(pwd)/outputs:/app/outputs:ro"
RUN_CMD="$RUN_CMD -v $(pwd)/imgs:/app/imgs"

# 添加镜像名称
RUN_CMD="$RUN_CMD $IMAGE_NAME"

# 显示运行信息
print_message $BLUE "=== Docker运行配置 ==="
echo "镜像: $IMAGE_NAME"
echo "容器名称: $CONTAINER_NAME"
echo "端口映射: $HOST_PORT:$CONTAINER_PORT"
echo "GPU支持: $GPU_SUPPORT"
echo "开发模式: $DEV_MODE"
echo "后台运行: $DETACHED"
echo ""

# 执行运行命令
print_message $YELLOW "启动容器..."
print_message $BLUE "执行命令: $RUN_CMD"
eval $RUN_CMD

if [[ $? -eq 0 ]]; then
    print_message $GREEN "✅ 容器启动成功"
    print_message $BLUE "访问地址: http://localhost:$HOST_PORT"
    print_message $BLUE "API文档: http://localhost:$HOST_PORT/docs"
    
    if [[ "$DETACHED" == true ]]; then
        print_message $YELLOW "容器在后台运行"
        print_message $BLUE "查看日志: docker logs -f $CONTAINER_NAME"
        print_message $BLUE "停止容器: docker stop $CONTAINER_NAME"
    fi
else
    print_message $RED "❌ 容器启动失败"
    exit 1
fi
