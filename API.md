# API 使用文档

本文档详细介绍遥感图像分类系统的 RESTful API 接口，包括端点说明、请求/响应格式、使用示例和错误处理。

## 📋 目录

- [API 概述](#api概述)
- [认证和授权](#认证和授权)
- [核心端点](#核心端点)
- [数据模型](#数据模型)
- [错误处理](#错误处理)
- [使用示例](#使用示例)
- [SDK 和客户端](#sdk和客户端)

## 🌐 API 概述

### 基础信息

- **基础 URL**: `http://localhost:8000`
- **API 前缀**: `/api`
- **版本**: `v1.0.0`
- **协议**: HTTP/HTTPS
- **数据格式**: JSON (除文件上传外)
- **字符编码**: UTF-8

### 特性

- ✅ RESTful 设计原则
- ✅ OpenAPI 3.0 规范
- ✅ 自动生成文档 (Swagger UI)
- ✅ 数据验证和类型安全
- ✅ 统一错误响应格式
- ✅ CORS 支持
- ✅ 文件上传支持

### 文档地址

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🔐 认证和授权

当前版本为开放 API，无需认证。生产环境建议添加适当的认证机制。

## 🔌 核心端点

### 1. 健康检查

检查服务健康状态。

```http
GET /health
```

**响应示例:**

```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0"
}
```

### 2. 系统信息

获取系统配置和环境信息。

```http
GET /api/info
```

**响应示例:**

```json
{
  "success": true,
  "system_info": {
    "device": "cuda",
    "cuda_available": true,
    "gpu_count": 1,
    "python_version": "3.11.0",
    "pytorch_version": "2.0.0",
    "api_version": "1.0.0",
    "platform": "Linux-5.4.0-74-generic-x86_64",
    "host": "0.0.0.0",
    "port": 8000,
    "debug": false
  }
}
```

### 3. 模型管理

#### 获取可用模型列表

```http
GET /api/models/
```

**响应示例:**

```json
{
  "success": true,
  "models": [
    {
      "model_key": "resnet50-原始",
      "model_type": "resnet50",
      "variant": "original",
      "device": "cuda",
      "available": true
    },
    {
      "model_key": "resnet50-剪枝",
      "model_type": "resnet50",
      "variant": "pruned",
      "device": "cuda",
      "available": true
    }
  ],
  "total_count": 2,
  "current_run_dir": "outputs/run_20240115_103000"
}
```

#### 获取模型比较

```http
GET /api/models/{model_type}/comparison/{comparison_type}
```

**路径参数:**

- `model_type`: 模型类型 (`resnet50`, `densenet201`, `swin_t`, `vit_s_16`)
- `comparison_type`: 比较类型 (`pruning`, `distillation`, `quantization`)

**响应示例:**

```json
{
  "success": true,
  "html_content": "<h3>RESNET50 剪枝模型与原始模型比较</h3><table>...</table>",
  "error": null
}
```

### 4. 图像预测

#### 预测图像分类

```http
POST /api/predict/
Content-Type: multipart/form-data
```

**请求参数:**

- `file`: 图像文件 (必需)
- `model_key`: 模型键名 (必需)

**响应示例:**

```json
{
  "success": true,
  "results": {
    "airplane": 0.8532,
    "bridge": 0.1234,
    "harbor": 0.0156,
    "runway": 0.0067,
    "freeway": 0.0011
  },
  "inference_time": "推理时间: 45.23 ms",
  "error": null
}
```

#### 获取预测模型列表

```http
GET /api/predict/models
```

**响应示例:**

```json
{
  "success": true,
  "models": ["resnet50-原始", "resnet50-剪枝", "densenet201-原始"],
  "total_count": 3
}
```

## 📊 数据模型

### PredictionRequest

```json
{
  "model_key": "string"
}
```

### PredictionResponse

```json
{
  "success": "boolean",
  "results": {
    "class_name": "float"
  },
  "inference_time": "string",
  "error": "string"
}
```

### ModelInfo

```json
{
  "model_key": "string",
  "model_type": "string",
  "variant": "string",
  "device": "string",
  "available": "boolean"
}
```

### ErrorResponse

```json
{
  "success": false,
  "error": "string",
  "detail": "string"
}
```

## ❌ 错误处理

### HTTP 状态码

| 状态码 | 含义                  | 描述           |
| ------ | --------------------- | -------------- |
| 200    | OK                    | 请求成功       |
| 400    | Bad Request           | 请求参数错误   |
| 404    | Not Found             | 资源不存在     |
| 422    | Unprocessable Entity  | 数据验证失败   |
| 500    | Internal Server Error | 服务器内部错误 |

### 错误响应格式

```json
{
  "success": false,
  "error": "错误描述",
  "detail": "详细错误信息"
}
```

### 常见错误

**文件上传错误:**

```json
{
  "success": false,
  "error": "上传的文件必须是图像格式"
}
```

**模型不存在错误:**

```json
{
  "success": false,
  "error": "无效的模型键名。可用模型: resnet50-原始, resnet50-剪枝"
}
```

**文件大小超限:**

```json
{
  "success": false,
  "error": "文件大小超过10MB限制"
}
```

## 💡 使用示例

### Python 示例

```python
import requests
import json

# 基础URL
BASE_URL = "http://localhost:8000"

# 1. 健康检查
def check_health():
    response = requests.get(f"{BASE_URL}/health")
    return response.json()

# 2. 获取系统信息
def get_system_info():
    response = requests.get(f"{BASE_URL}/api/info")
    return response.json()

# 3. 获取可用模型
def get_models():
    response = requests.get(f"{BASE_URL}/api/models/")
    return response.json()

# 4. 图像预测
def predict_image(image_path, model_key):
    with open(image_path, 'rb') as f:
        files = {'file': f}
        data = {'model_key': model_key}
        response = requests.post(
            f"{BASE_URL}/api/predict/",
            files=files,
            data=data
        )
    return response.json()

# 5. 获取模型比较
def get_model_comparison(model_type, comparison_type):
    response = requests.get(
        f"{BASE_URL}/api/models/{model_type}/comparison/{comparison_type}"
    )
    return response.json()

# 使用示例
if __name__ == "__main__":
    # 检查服务状态
    health = check_health()
    print(f"服务状态: {health['status']}")

    # 获取可用模型
    models = get_models()
    if models['success']:
        print(f"可用模型数量: {models['total_count']}")

        # 使用第一个模型进行预测
        if models['models']:
            model_key = models['models'][0]['model_key']
            result = predict_image('test_image.jpg', model_key)

            if result['success']:
                print("预测结果:")
                for class_name, confidence in result['results'].items():
                    print(f"  {class_name}: {confidence:.4f}")
                print(f"推理时间: {result['inference_time']}")
```

### JavaScript 示例

```javascript
// 基础URL
const BASE_URL = "http://localhost:8000";

// 1. 健康检查
async function checkHealth() {
  const response = await fetch(`${BASE_URL}/health`);
  return await response.json();
}

// 2. 获取可用模型
async function getModels() {
  const response = await fetch(`${BASE_URL}/api/models/`);
  return await response.json();
}

// 3. 图像预测
async function predictImage(file, modelKey) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("model_key", modelKey);

  const response = await fetch(`${BASE_URL}/api/predict/`, {
    method: "POST",
    body: formData,
  });

  return await response.json();
}

// 4. 获取模型比较
async function getModelComparison(modelType, comparisonType) {
  const response = await fetch(
    `${BASE_URL}/api/models/${modelType}/comparison/${comparisonType}`
  );
  return await response.json();
}

// 使用示例
document.addEventListener("DOMContentLoaded", async () => {
  try {
    // 检查服务状态
    const health = await checkHealth();
    console.log("服务状态:", health.status);

    // 获取可用模型
    const models = await getModels();
    if (models.success) {
      console.log("可用模型:", models.models);
    }
  } catch (error) {
    console.error("API调用失败:", error);
  }
});
```

### cURL 示例

```bash
# 1. 健康检查
curl -X GET "http://localhost:8000/health"

# 2. 获取系统信息
curl -X GET "http://localhost:8000/api/info"

# 3. 获取可用模型
curl -X GET "http://localhost:8000/api/models/"

# 4. 图像预测
curl -X POST "http://localhost:8000/api/predict/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_image.jpg" \
  -F "model_key=resnet50-原始"

# 5. 获取模型比较
curl -X GET "http://localhost:8000/api/models/resnet50/comparison/pruning"
```

## 🛠️ SDK 和客户端

### Python SDK 示例

```python
class RemoteSensingAPI:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()

    def health_check(self):
        """健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()

    def get_models(self):
        """获取可用模型列表"""
        response = self.session.get(f"{self.base_url}/api/models/")
        response.raise_for_status()
        return response.json()

    def predict(self, image_path, model_key):
        """预测图像分类"""
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {'model_key': model_key}
            response = self.session.post(
                f"{self.base_url}/api/predict/",
                files=files,
                data=data
            )
        response.raise_for_status()
        return response.json()

    def get_comparison(self, model_type, comparison_type):
        """获取模型比较"""
        response = self.session.get(
            f"{self.base_url}/api/models/{model_type}/comparison/{comparison_type}"
        )
        response.raise_for_status()
        return response.json()

# 使用示例
api = RemoteSensingAPI()
models = api.get_models()
result = api.predict('image.jpg', 'resnet50-原始')
```

## 📝 最佳实践

### 1. 错误处理

```python
try:
    result = predict_image('image.jpg', 'resnet50-原始')
    if result['success']:
        # 处理成功结果
        predictions = result['results']
    else:
        # 处理业务错误
        print(f"预测失败: {result['error']}")
except requests.exceptions.RequestException as e:
    # 处理网络错误
    print(f"网络请求失败: {e}")
```

### 2. 文件上传优化

```python
# 检查文件大小
MAX_SIZE = 10 * 1024 * 1024  # 10MB
if os.path.getsize(image_path) > MAX_SIZE:
    raise ValueError("文件大小超过限制")

# 检查文件类型
allowed_types = ['.jpg', '.jpeg', '.png', '.gif']
if not any(image_path.lower().endswith(ext) for ext in allowed_types):
    raise ValueError("不支持的文件类型")
```

### 3. 并发请求

```python
import asyncio
import aiohttp

async def async_predict(session, image_path, model_key):
    with open(image_path, 'rb') as f:
        data = aiohttp.FormData()
        data.add_field('file', f)
        data.add_field('model_key', model_key)

        async with session.post(f"{BASE_URL}/api/predict/", data=data) as response:
            return await response.json()

# 批量预测
async def batch_predict(image_paths, model_key):
    async with aiohttp.ClientSession() as session:
        tasks = [async_predict(session, path, model_key) for path in image_paths]
        return await asyncio.gather(*tasks)
```

### 4. 重试机制

```python
import time
from functools import wraps

def retry(max_attempts=3, delay=1):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    if attempt == max_attempts - 1:
                        raise
                    time.sleep(delay * (2 ** attempt))  # 指数退避
            return wrapper
        return decorator

@retry(max_attempts=3)
def robust_predict(image_path, model_key):
    return predict_image(image_path, model_key)
```

## 🔗 相关资源

- **交互式 API 文档**: http://localhost:8000/docs
- **API 规范文件**: http://localhost:8000/openapi.json
- **项目 GitHub**: [项目链接]
- **问题反馈**: [Issues 链接]
