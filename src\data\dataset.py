import os
import random
import numpy as np
from PIL import Image
from typing import Tuple, List, Dict, Optional, Callable

import torch
from torch.utils.data import Dataset, DataLoader, random_split
from torchvision import transforms


class PatternNetDataset(Dataset):
    """PatternNet遥感场景数据集加载类"""
    
    def __init__(self, root_dir: str, transform: Optional[Callable] = None, split: str = 'train'):
        """
        初始化PatternNet数据集
        
        Args:
            root_dir: 数据集根目录，包含各个类别的文件夹
            transform: 数据转换操作
            split: 数据集划分，可选'train', 'val', 'test'
        """
        self.root_dir = root_dir
        self.transform = transform
        self.split = split
        
        # 获取所有类别
        self.classes = sorted([d for d in os.listdir(root_dir) if os.path.isdir(os.path.join(root_dir, d))])
        self.class_to_idx = {cls_name: i for i, cls_name in enumerate(self.classes)}
        
        # 获取所有图片路径和标签
        self.samples = self._make_dataset()
        
        # 确保数据集已准备好
        if len(self.samples) == 0:
            raise RuntimeError(f"Found 0 files in {root_dir}")
    
    def _make_dataset(self) -> List[Tuple[str, int]]:
        """
        创建数据集样本列表
        
        Returns:
            包含(图片路径, 类别索引)的列表
        """
        samples = []
        for class_name in self.classes:
            class_dir = os.path.join(self.root_dir, class_name)
            class_idx = self.class_to_idx[class_name]
            
            # 获取该类别下所有图片
            for img_name in os.listdir(class_dir):
                if img_name.endswith(('.jpg', '.jpeg', '.png')):
                    img_path = os.path.join(class_dir, img_name)
                    samples.append((img_path, class_idx))
        
        # 为保证结果可复现，设置随机种子
        random.seed(42)
        random.shuffle(samples)
        
        return samples
    
    def __len__(self) -> int:
        """返回数据集大小"""
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """
        获取指定索引的数据样本
        
        Args:
            idx: 样本索引
            
        Returns:
            (图像张量, 类别索引)元组
        """
        img_path, label = self.samples[idx]
        
        # 读取图像并转换为RGB模式
        image = Image.open(img_path).convert('RGB')
        
        # 应用转换
        if self.transform:
            image = self.transform(image)
        
        return image, label


def get_transforms(split: str) -> transforms.Compose:
    """
    根据数据集划分获取对应的数据转换
    
    Args:
        split: 数据集划分，可选'train', 'val', 'test'
        
    Returns:
        转换操作组合
    """
    if split == 'train':
        return transforms.Compose([
            transforms.RandomResizedCrop(224),
            transforms.RandomHorizontalFlip(),
            transforms.ColorJitter(brightness=0.2, contrast=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:  # val or test
        return transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])


def create_dataloaders(
    data_dir: str,
    batch_size: int,
    train_ratio: float = 0.7,
    val_ratio: float = 0.15,
    num_workers: int = 4,
    pin_memory: bool = True
) -> Tuple[DataLoader, DataLoader, DataLoader, List[str]]:
    """
    创建训练、验证和测试数据加载器
    
    Args:
        data_dir: 数据集目录
        batch_size: 批次大小
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        num_workers: 数据加载的工作线程数
        pin_memory: 是否将数据固定到内存中
        
    Returns:
        (训练数据加载器, 验证数据加载器, 测试数据加载器, 类别列表)
    """
    # 创建完整数据集
    full_dataset = PatternNetDataset(
        root_dir=data_dir, 
        transform=get_transforms('train')
    )
    
    # 计算各个集合的大小
    dataset_size = len(full_dataset)
    train_size = int(train_ratio * dataset_size)
    val_size = int(val_ratio * dataset_size)
    test_size = dataset_size - train_size - val_size
    
    # 划分数据集
    train_dataset, val_dataset, test_dataset = random_split(
        full_dataset, [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # 为验证集和测试集设置正确的转换
    val_dataset.dataset = PatternNetDataset(
        root_dir=data_dir, 
        transform=get_transforms('val')
    )
    val_dataset.indices = val_dataset.indices
    
    test_dataset.dataset = PatternNetDataset(
        root_dir=data_dir, 
        transform=get_transforms('test')
    )
    test_dataset.indices = test_dataset.indices
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    test_loader = DataLoader(
        test_dataset, 
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    return train_loader, val_loader, test_loader, full_dataset.classes


if __name__ == "__main__":
    # 测试代码
    import matplotlib.pyplot as plt
    
    data_dir = "../../PatternNet/images"
    train_loader, val_loader, test_loader, classes = create_dataloaders(
        data_dir, batch_size=4
    )
    
    print(f"类别数量: {len(classes)}")
    print(f"训练集大小: {len(train_loader.dataset)}")
    print(f"验证集大小: {len(val_loader.dataset)}")
    print(f"测试集大小: {len(test_loader.dataset)}")
    
    # 显示一批次的图像
    images, labels = next(iter(train_loader))
    
    # 逆正规化以便显示
    mean = torch.tensor([0.485, 0.456, 0.406]).reshape(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).reshape(3, 1, 1)
    
    plt.figure(figsize=(12, 6))
    for i in range(min(4, len(images))):
        img = images[i].permute(1, 2, 0).numpy() * std.permute(1, 2, 0).numpy() + mean.permute(1, 2, 0).numpy()
        img = np.clip(img, 0, 1)
        
        plt.subplot(1, 4, i+1)
        plt.imshow(img)
        plt.title(f"Class: {classes[labels[i]]}")
        plt.axis('off')
    
    plt.tight_layout()
    plt.savefig("sample_images.png")
    plt.close() 