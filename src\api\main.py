from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import sys
import os
import platform
import torch
from datetime import datetime, timezone

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from api.config import settings
from api.routers import models, prediction
from api.schemas.models import HealthResponse, SystemInfoResponse

# 创建FastAPI应用实例
app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(models.router, prefix=settings.API_PREFIX)
app.include_router(prediction.router, prefix=settings.API_PREFIX)

# 静态文件服务
static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")


@app.get("/", response_class=HTMLResponse)
async def root():
    """
    根路径，返回前端应用页面
    """
    try:
        # 尝试返回前端应用
        static_dir = os.path.join(os.path.dirname(__file__), "..", "static")
        index_path = os.path.join(static_dir, "index.html")

        if os.path.exists(index_path):
            with open(index_path, "r", encoding="utf-8") as f:
                return f.read()
    except Exception as e:
        print(f"无法加载前端页面: {e}")

    # 如果前端页面不存在，返回API信息页面
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>遥感图像分类API</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 40px; }
            .api-links { display: flex; gap: 20px; justify-content: center; margin: 20px 0; }
            .api-link { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
            .api-link:hover { background: #0056b3; }
            .features { margin: 40px 0; }
            .feature { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛰️ 遥感图像分类API</h1>
                <p>基于深度学习的遥感场景分类系统</p>
            </div>

            <div class="api-links">
                <a href="/docs" class="api-link">📚 API文档 (Swagger)</a>
                <a href="/redoc" class="api-link">📖 API文档 (ReDoc)</a>
                <a href="/health" class="api-link">💚 健康检查</a>
            </div>

            <div class="features">
                <div class="feature">
                    <h3>🤖 支持的模型</h3>
                    <p>ResNet50, DenseNet201, ViT-S/16, Swin-T 及其优化版本（剪枝、蒸馏、量化）</p>
                </div>

                <div class="feature">
                    <h3>🔍 主要功能</h3>
                    <p>• 图像分类预测<br>• 模型性能比较<br>• 多种模型优化技术支持</p>
                </div>

                <div class="feature">
                    <h3>🚀 快速开始</h3>
                    <p>访问 <a href="/docs">/docs</a> 查看完整的API文档和交互式测试界面</p>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html_content


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查端点

    Returns:
        服务健康状态
    """
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc).isoformat(),
        version=settings.API_VERSION,
    )


@app.get(f"{settings.API_PREFIX}/info", response_model=SystemInfoResponse)
async def get_system_info():
    """
    获取系统信息

    Returns:
        系统配置和环境信息
    """
    try:
        system_info = {
            "device": settings.get_device(),
            "cuda_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "python_version": platform.python_version(),
            "pytorch_version": torch.__version__,
            "api_version": settings.API_VERSION,
            "platform": platform.platform(),
            "host": settings.HOST,
            "port": settings.PORT,
            "debug": settings.DEBUG,
        }

        if torch.cuda.is_available():
            system_info["gpu_name"] = torch.cuda.get_device_name(0)
            system_info["gpu_memory"] = (
                f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB"
            )

        return SystemInfoResponse(success=True, system_info=system_info)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):  # noqa: ARG001
    """
    全局异常处理器
    """
    return {
        "success": False,
        "error": "内部服务器错误",
        "detail": str(exc) if settings.DEBUG else "请联系管理员",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
