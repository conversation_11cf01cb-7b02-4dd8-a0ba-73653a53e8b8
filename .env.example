# 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 服务器配置
HOST=0.0.0.0
PORT=8000
DEBUG=false

# 模型配置
MODEL_BASE_DIR=outputs
DEVICE=auto
# 可选值: auto, cuda, cpu
# auto: 自动检测CUDA可用性
# cuda: 强制使用GPU
# cpu: 强制使用CPU

# 图像上传配置
UPLOAD_DIR=imgs
MAX_UPLOAD_SIZE=10485760
# 最大上传文件大小，单位：字节 (默认10MB)

# 日志配置
LOG_LEVEL=INFO
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL

# CORS配置
CORS_ORIGINS=*
# 允许的跨域来源，多个用逗号分隔
# 生产环境建议设置具体域名，如: http://localhost:3000,https://yourdomain.com

# 模型缓存配置
MODEL_CACHE_SIZE=1
# 同时缓存的模型数量，建议根据内存大小调整

# API配置
API_PREFIX=/api
API_TITLE=遥感图像分类API
API_VERSION=1.0.0
API_DESCRIPTION=基于深度学习的遥感场景分类系统API
