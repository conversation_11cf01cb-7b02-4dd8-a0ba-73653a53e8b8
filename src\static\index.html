<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>遥感图像分类系统</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛰️</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>🛰️ 遥感图像分类系统</h1>
            <p>基于深度学习的遥感场景分类与模型比较平台</p>
        </header>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 标签页导航 -->
            <div class="tabs">
                <button class="tab-button active" onclick="switchTab('prediction')">
                    🔍 图像预测
                </button>
                <button class="tab-button" onclick="switchTab('comparison')">
                    📊 模型比较
                </button>
            </div>

            <!-- 图像预测标签页 -->
            <div id="prediction" class="tab-content active">
                <div class="prediction-section">
                    <!-- 左侧：上传和控制 -->
                    <div class="upload-section">
                        <!-- 模型选择 -->
                        <div class="model-selection">
                            <label for="modelSelect">选择模型：</label>
                            <select id="modelSelect" class="model-select">
                                <option value="">正在加载模型...</option>
                            </select>
                        </div>

                        <!-- 图像上传区域 -->
                        <div class="upload-area" onclick="document.getElementById('imageInput').click()">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击或拖拽上传图像</div>
                            <div class="upload-hint">支持 JPG, PNG, GIF 格式，最大 10MB</div>
                            <input type="file" id="imageInput" accept="image/*">
                        </div>

                        <!-- 图像预览 -->
                        <div id="imagePreview" class="image-preview" style="display: none;">
                            <img id="previewImage" class="preview-image" alt="预览图像">
                        </div>

                        <!-- 预测按钮 -->
                        <button id="predictButton" class="predict-button" disabled>
                            开始预测
                        </button>
                    </div>

                    <!-- 右侧：结果显示 -->
                    <div class="results-section">
                        <h3>预测结果</h3>
                        
                        <!-- 加载状态 -->
                        <div id="predictionLoading" class="loading">
                            <div class="spinner"></div>
                            <p>正在预测中...</p>
                        </div>

                        <!-- 预测结果 -->
                        <div id="predictionResults" class="prediction-results" style="display: none;">
                            <div id="resultsContent"></div>
                        </div>

                        <!-- 推理时间 -->
                        <div id="inferenceTime" class="inference-time" style="display: none;"></div>

                        <!-- 消息显示 -->
                        <div id="predictionMessage"></div>
                    </div>
                </div>
            </div>

            <!-- 模型比较标签页 -->
            <div id="comparison" class="tab-content">
                <!-- 比较控制 -->
                <div class="comparison-controls">
                    <div>
                        <label for="modelTypeSelect">模型类型：</label>
                        <select id="modelTypeSelect">
                            <option value="resnet50">ResNet50</option>
                            <option value="densenet201">DenseNet201</option>
                            <option value="swin_t">Swin Transformer</option>
                            <option value="vit_s_16">Vision Transformer</option>
                        </select>
                    </div>
                    <div>
                        <label for="comparisonTypeSelect">比较类型：</label>
                        <select id="comparisonTypeSelect">
                            <option value="pruning">剪枝比较</option>
                            <option value="distillation">蒸馏比较</option>
                            <option value="quantization">量化比较</option>
                        </select>
                    </div>
                </div>

                <!-- 比较按钮 -->
                <button id="compareButton" class="compare-button">
                    生成比较报告
                </button>

                <!-- 加载状态 -->
                <div id="comparisonLoading" class="loading">
                    <div class="spinner"></div>
                    <p>正在生成比较报告...</p>
                </div>

                <!-- 比较结果 -->
                <div id="comparisonResults" class="comparison-results">
                    <p style="text-align: center; color: #6c757d; margin-top: 60px;">
                        选择模型类型和比较类型，然后点击"生成比较报告"查看详细对比数据
                    </p>
                </div>

                <!-- 消息显示 -->
                <div id="comparisonMessage"></div>
            </div>
        </main>
    </div>

    <script src="/static/js/app.js"></script>
</body>
</html>
