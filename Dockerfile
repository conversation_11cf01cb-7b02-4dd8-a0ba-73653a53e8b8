# 使用 NVIDIA CUDA 官方镜像作为基础镜像
FROM nvidia/cuda:12.2.2-base-ubuntu22.04

# 安装 Python 和其他必要工具
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 requirements.txt 文件并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir  -r requirements.txt

# 复制项目文件
COPY src /app/src
COPY outputs /app/outputs
COPY imgs /app/imgs


# 设置容器启动时执行的命令
CMD ["python3", "src/app.py"]  