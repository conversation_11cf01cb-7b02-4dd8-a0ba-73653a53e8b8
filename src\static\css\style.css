/* 遥感图像分类前端样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

/* 标签页样式 */
.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: white;
}

/* 标签页内容 */
.tab-content {
    display: none;
    padding: 30px;
}

.tab-content.active {
    display: block;
}

/* 预测界面样式 */
.prediction-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

/* 模型选择 */
.model-selection {
    margin-bottom: 25px;
}

.model-selection label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.model-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.model-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* 图像上传区域 */
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.upload-area:hover {
    border-color: #007bff;
    background: #e7f3ff;
}

.upload-area.dragover {
    border-color: #007bff;
    background: #e7f3ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.upload-text {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.upload-hint {
    font-size: 0.9rem;
    color: #adb5bd;
}

#imageInput {
    display: none;
}

/* 图像预览 */
.image-preview {
    margin-top: 20px;
    text-align: center;
}

.preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 预测按钮 */
.predict-button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.predict-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.predict-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

/* 结果显示区域 */
.results-section h3 {
    margin-bottom: 20px;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.prediction-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 500;
    color: #495057;
}

.result-confidence {
    font-weight: 600;
    color: #007bff;
}

.confidence-bar {
    width: 100px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    margin-left: 10px;
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #007bff);
    transition: width 0.5s ease;
}

/* 推理时间显示 */
.inference-time {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 10px;
    text-align: center;
    font-weight: 500;
    color: #0056b3;
}

/* 模型比较界面 */
.comparison-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.comparison-controls label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

.comparison-controls select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
}

.compare-button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 25px;
}

.compare-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40,167,69,0.3);
}

.comparison-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 200px;
}

/* 加载状态 */
.loading {
    display: none;
    text-align: center;
    padding: 40px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误和成功消息 */
.message {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
}

.message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.message.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .prediction-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-button {
        border-bottom: 1px solid #dee2e6;
        border-right: none;
    }
    
    .tab-button.active {
        border-bottom-color: #dee2e6;
        border-left: 3px solid #007bff;
    }
    
    .comparison-controls {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8rem;
    }
    
    .header p {
        font-size: 1rem;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .upload-area {
        padding: 30px 15px;
    }
    
    .upload-icon {
        font-size: 2.5rem;
    }
}
