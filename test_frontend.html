<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <h1>🧪 前端功能测试</h1>
    
    <div class="test-section">
        <h2>API连接测试</h2>
        <button class="test-button" onclick="testHealthCheck()">健康检查</button>
        <button class="test-button" onclick="testSystemInfo()">系统信息</button>
        <button class="test-button" onclick="testModelsList()">模型列表</button>
        <div id="apiResults" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>前端页面测试</h2>
        <button class="test-button" onclick="openMainApp()">打开主应用</button>
        <button class="test-button" onclick="testStaticFiles()">测试静态文件</button>
        <div id="frontendResults" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>功能测试</h2>
        <button class="test-button" onclick="testImageUpload()">测试图像上传</button>
        <button class="test-button" onclick="testModelComparison()">测试模型比较</button>
        <div id="functionalResults" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = '/api';
        
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.style.display = 'block';
        }
        
        async function testHealthCheck() {
            try {
                const response = await fetch('/health');
                const data = await response.json();
                showResult('apiResults', `✅ 健康检查成功: ${data.status} (${data.timestamp})`);
            } catch (error) {
                showResult('apiResults', `❌ 健康检查失败: ${error.message}`, true);
            }
        }
        
        async function testSystemInfo() {
            try {
                const response = await fetch(`${API_BASE}/info`);
                const data = await response.json();
                if (data.success) {
                    const info = data.system_info;
                    showResult('apiResults', `✅ 系统信息: 设备=${info.device}, CUDA=${info.cuda_available}, PyTorch=${info.pytorch_version}`);
                } else {
                    showResult('apiResults', `❌ 系统信息获取失败`, true);
                }
            } catch (error) {
                showResult('apiResults', `❌ 系统信息请求失败: ${error.message}`, true);
            }
        }
        
        async function testModelsList() {
            try {
                const response = await fetch(`${API_BASE}/models/`);
                const data = await response.json();
                if (data.success) {
                    showResult('apiResults', `✅ 模型列表: 找到 ${data.total_count} 个模型`);
                } else {
                    showResult('apiResults', `❌ 模型列表获取失败`, true);
                }
            } catch (error) {
                showResult('apiResults', `❌ 模型列表请求失败: ${error.message}`, true);
            }
        }
        
        function openMainApp() {
            window.open('/', '_blank');
            showResult('frontendResults', '✅ 主应用已在新标签页中打开');
        }
        
        async function testStaticFiles() {
            try {
                const cssResponse = await fetch('/static/css/style.css');
                const jsResponse = await fetch('/static/js/app.js');
                
                if (cssResponse.ok && jsResponse.ok) {
                    showResult('frontendResults', '✅ 静态文件加载成功 (CSS + JS)');
                } else {
                    showResult('frontendResults', '❌ 静态文件加载失败', true);
                }
            } catch (error) {
                showResult('frontendResults', `❌ 静态文件测试失败: ${error.message}`, true);
            }
        }
        
        function testImageUpload() {
            // 创建一个测试用的小图像
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#007bff';
            ctx.fillRect(0, 0, 100, 100);
            
            canvas.toBlob(async (blob) => {
                try {
                    const formData = new FormData();
                    formData.append('file', blob, 'test.jpg');
                    formData.append('model_key', 'test-model');
                    
                    const response = await fetch(`${API_BASE}/predict/`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (response.status === 400) {
                        showResult('functionalResults', '✅ 图像上传接口正常 (验证失败是预期的)');
                    } else {
                        showResult('functionalResults', `✅ 图像上传接口响应: ${response.status}`);
                    }
                } catch (error) {
                    showResult('functionalResults', `❌ 图像上传测试失败: ${error.message}`, true);
                }
            }, 'image/jpeg');
        }
        
        async function testModelComparison() {
            try {
                const response = await fetch(`${API_BASE}/models/resnet50/comparison/pruning`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('functionalResults', '✅ 模型比较接口正常');
                } else {
                    showResult('functionalResults', `✅ 模型比较接口响应: ${response.status} (可能是数据不存在)`);
                }
            } catch (error) {
                showResult('functionalResults', `❌ 模型比较测试失败: ${error.message}`, true);
            }
        }
        
        // 页面加载时自动运行基本测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testHealthCheck();
            }, 500);
        });
    </script>
</body>
</html>
