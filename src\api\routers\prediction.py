from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from PIL import Image
import io
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from api.schemas.models import PredictionResponse, ErrorResponse
from api.services.model_service import ModelService
from api.services.prediction_service import PredictionService

router = APIRouter(prefix="/predict", tags=["prediction"])

# 全局服务实例
model_service = ModelService()
prediction_service = PredictionService(model_service)


def get_model_service():
    """依赖注入：获取模型服务实例"""
    return model_service


def get_prediction_service():
    """依赖注入：获取预测服务实例"""
    return prediction_service


@router.post("/", response_model=PredictionResponse)
async def predict_image(
    file: UploadFile = File(..., description="上传的图像文件"),
    model_key: str = Form(..., description="模型键名，格式为 'model_type-variant'"),
    prediction_svc: PredictionService = Depends(get_prediction_service)
):
    """
    图像预测接口
    
    Args:
        file: 上传的图像文件
        model_key: 模型键名
    
    Returns:
        预测结果和推理时间
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="上传的文件必须是图像格式"
            )
        
        # 验证文件大小 (10MB限制)
        max_size = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail="文件大小超过10MB限制"
            )
        
        # 读取图像
        try:
            image = Image.open(io.BytesIO(file_content))
            # 确保图像是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"无法读取图像文件: {str(e)}"
            )
        
        # 验证模型键名
        available_models = prediction_svc.model_service.get_available_models()
        if not available_models:
            # 尝试发现模型
            prediction_svc.model_service.discover_available_models()
            available_models = prediction_svc.model_service.get_available_models()
        
        if model_key not in available_models:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模型键名。可用模型: {', '.join(available_models)}"
            )
        
        # 执行预测
        results, time_info = prediction_svc.predict(image, model_key)
        
        # 检查预测结果
        if isinstance(results, str) and results.startswith("预测失败"):
            return PredictionResponse(
                success=False,
                results=None,
                inference_time=None,
                error=results
            )
        
        if isinstance(results, str):  # 其他错误情况
            return PredictionResponse(
                success=False,
                results=None,
                inference_time=time_info,
                error=results
            )
        
        return PredictionResponse(
            success=True,
            results=results,
            inference_time=time_info,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"预测过程中发生错误: {str(e)}"
        )


@router.get("/models", response_model=dict)
async def get_prediction_models(
    model_svc: ModelService = Depends(get_model_service)
):
    """
    获取可用于预测的模型列表
    
    Returns:
        可用模型键名列表
    """
    try:
        # 确保模型已发现
        if not model_svc.model_paths:
            model_svc.discover_available_models()
        
        available_models = model_svc.get_available_models()
        
        return {
            "success": True,
            "models": available_models,
            "total_count": len(available_models)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取预测模型列表失败: {str(e)}"
        )
