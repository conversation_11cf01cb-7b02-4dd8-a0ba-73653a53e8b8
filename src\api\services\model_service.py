import os
import json
import torch
from typing import Dict, Optional, List
import traceback

from models.base_model import create_model
from optimization.distillation import create_student_model


class ModelService:
    """
    模型管理服务类

    封装模型发现、加载、卸载等功能，保持与原app.py相同的业务逻辑
    """

    def __init__(self):
        """初始化模型服务"""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.models = {}  # 存储已加载的模型
        self.model_infos = {}  # 存储模型信息
        self.model_paths = {}  # 存储模型路径
        self.classes = []  # 类别列表
        self.current_run_dir = ""  # 当前运行目录

    def find_latest_run_directory(
        self, base_output_dir: str = "outputs"
    ) -> Optional[str]:
        """
        查找最新的运行目录

        Args:
            base_output_dir: 基础输出目录

        Returns:
            最新运行目录的路径
        """
        run_dirs = []
        try:
            # 查找所有以run_开头的目录
            for item in os.listdir(base_output_dir):
                full_path = os.path.join(base_output_dir, item)
                if item.startswith("run_") and os.path.isdir(full_path):
                    run_dirs.append(full_path)
        except Exception as e:
            print(f"列出运行目录时出错: {e}")
            return None

        if not run_dirs:
            print("没有找到任何运行目录")
            return None

        # 按创建时间排序，返回最新的
        return max(run_dirs, key=os.path.getctime)

    def read_evaluation_results(self, file_path: str) -> Dict:
        """
        读取评估结果文件

        Args:
            file_path: 评估结果文件路径

        Returns:
            包含评估指标的字典
        """
        metrics = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, "r") as f:
                    for line in f:
                        if ":" in line:
                            key, value = line.strip().split(":", 1)
                            metrics[key.strip()] = float(value.strip())
            except Exception as e:
                print(f"读取评估结果时出错: {e}")
        return metrics

    def read_parameter_comparison(self, file_path: str) -> Dict:
        """
        读取参数比较文件

        Args:
            file_path: 参数比较文件路径

        Returns:
            包含参数比较的字典
        """
        comparison = {}
        if os.path.exists(file_path):
            try:
                with open(file_path, "r") as f:
                    lines = f.readlines()
                # 确保至少有3行（表头+原始模型+剪枝模型）
                if len(lines) >= 3:
                    # 第二行是原始模型
                    original_line = lines[1].strip()
                    # 第三行是剪枝模型
                    pruned_line = lines[2].strip()

                    # 解析原始模型行
                    original_parts = original_line.split()
                    print(original_parts)
                    if len(original_parts) >= 4:
                        # 处理原始模型
                        comparison["Original"] = {
                            "model_name": original_parts[0] + original_parts[1],
                            "nonzero_params": original_parts[3].replace(",", ""),
                            "sparsity": original_parts[4],
                        }

                    # 解析剪枝模型行
                    pruned_parts = pruned_line.split()
                    if len(pruned_parts) >= 4:
                        # 处理剪枝模型
                        comparison["Pruned"] = {
                            "model_name": pruned_parts[0] + pruned_parts[1],
                            "nonzero_params": pruned_parts[3].replace(",", ""),
                            "sparsity": pruned_parts[4],
                        }

                    if "Original" in comparison:
                        print(
                            f"原始模型非零参数: {comparison['Original']['nonzero_params']}, 稀疏度: {comparison['Original']['sparsity']}"
                        )
                    if "Pruned" in comparison:
                        print(
                            f"剪枝模型非零参数: {comparison['Pruned']['nonzero_params']}, 稀疏度: {comparison['Pruned']['sparsity']}"
                        )
                else:
                    print(f"参数比较文件格式错误，行数不足: {len(lines)}")

            except Exception as e:
                print(f"读取参数比较文件时出错: {e}")
                traceback.print_exc()
        else:
            print(f"参数比较文件不存在: {file_path}")

        return comparison

    def get_model_info(self, model_type: str, run_dir: str) -> Dict:
        """
        获取指定模型类型的评估信息

        Args:
            model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
            run_dir: 运行目录路径

        Returns:
            模型信息字典
        """
        model_info = {
            "original": None,
            "pruned": None,
            "distilled": None,
            "quantized": None,
        }

        # 模型目录
        model_dir = os.path.join(run_dir, model_type)
        if not os.path.exists(model_dir):
            print(f"模型目录不存在: {model_dir}")
            return model_info

        # 1. 原始模型
        original_model_path = os.path.join(model_dir, "model.pth")
        original_eval_path = os.path.join(model_dir, "evaluation_results.txt")

        if os.path.exists(original_model_path) and os.path.exists(original_eval_path):
            metrics = self.read_evaluation_results(original_eval_path)

            model_info["original"] = {
                "path": original_model_path,
                "size_mb": os.path.getsize(original_model_path) / (1024 * 1024),
                "metrics": metrics,
            }

        # 2. 剪枝模型
        pruned_dir = os.path.join(model_dir, "pruned")
        pruned_global_dir = os.path.join(pruned_dir, "global_50")
        pruned_model_path = os.path.join(pruned_global_dir, "pruned_global_50.pth")
        pruned_eval_path = os.path.join(pruned_global_dir, "evaluation_results.txt")
        param_comparison_path = os.path.join(pruned_dir, "parameter_comparison.txt")

        if os.path.exists(pruned_model_path) and os.path.exists(pruned_eval_path):
            metrics = self.read_evaluation_results(pruned_eval_path)
            comparison = self.read_parameter_comparison(param_comparison_path)

            model_info["pruned"] = {
                "path": pruned_model_path,
                "size_mb": os.path.getsize(pruned_model_path) / (1024 * 1024),
                "metrics": metrics,
                "comparison": comparison,
            }

        # 3. 蒸馏模型
        distilled_dir = os.path.join(model_dir, "distilled")
        distilled_model_path = os.path.join(distilled_dir, "student_mobilenetv2.pth")
        distilled_eval_path = os.path.join(distilled_dir, "evaluation_results.txt")

        if os.path.exists(distilled_model_path) and os.path.exists(distilled_eval_path):
            metrics = self.read_evaluation_results(distilled_eval_path)

            model_info["distilled"] = {
                "path": distilled_model_path,
                "size_mb": os.path.getsize(distilled_model_path) / (1024 * 1024),
                "metrics": metrics,
            }

        # 4. 量化模型 (仅适用于resnet50)
        if model_type == "resnet50":
            quantized_dir = os.path.join(model_dir, "quantized")
            quantized_model_path = os.path.join(quantized_dir, "quantized.pt")
            quantized_eval_path = os.path.join(quantized_dir, "evaluation_results.txt")

            if os.path.exists(quantized_model_path) and os.path.exists(
                quantized_eval_path
            ):
                metrics = self.read_evaluation_results(quantized_eval_path)

                model_info["quantized"] = {
                    "path": quantized_model_path,
                    "size_mb": os.path.getsize(quantized_model_path) / (1024 * 1024),
                    "metrics": metrics,
                }

        return model_info

    def discover_available_models(self, run_dir: Optional[str] = None) -> Optional[str]:
        """
        发现所有可用模型并记录路径信息

        Args:
            run_dir: 运行目录路径，如果为None则自动查找最新的

        Returns:
            运行目录路径
        """
        # 重置实例变量
        self.model_infos = {}
        self.model_paths = {}

        # 如果未指定运行目录，查找最新的
        if run_dir is None:
            run_dir = self.find_latest_run_directory()
            if run_dir is None:
                print("未找到任何运行目录")
                return None

        self.current_run_dir = run_dir
        print(f"使用运行目录: {run_dir}")

        # 加载类别列表
        try:
            classes_path = os.path.join(run_dir, "classes.json")
            if os.path.exists(classes_path):
                with open(classes_path, "r") as f:
                    self.classes = json.load(f)
            else:
                # 默认PatternNet数据集类别
                self.classes = [
                    "airplane",
                    "baseball_field",
                    "basketball_court",
                    "beach",
                    "bridge",
                    "cemetery",
                    "chaparral",
                    "christmas_tree_farm",
                    "closed_road",
                    "coastal_mansion",
                    "crosswalk",
                    "dense_residential",
                    "ferry_terminal",
                    "football_field",
                    "forest",
                    "freeway",
                    "golf_course",
                    "harbor",
                    "intersection",
                    "mobile_home_park",
                    "nursing_home",
                    "oil_gas_field",
                    "oil_well",
                    "overpass",
                    "parking_lot",
                    "parking_space",
                    "railway",
                    "river",
                    "runway",
                    "runway_marking",
                    "shipping_yard",
                    "solar_panel",
                    "sparse_residential",
                    "storage_tank",
                    "swimming_pool",
                    "tennis_court",
                    "transformer_station",
                    "wastewater_treatment_plant",
                ]
        except Exception as e:
            print(f"加载类别列表时出错: {e}")
            # 使用默认类别列表
            self.classes = [f"class_{i}" for i in range(38)]

        print(f"类别数量: {len(self.classes)}")

        # 查找四种模型类型
        model_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]

        for model_type in model_types:
            model_dir = os.path.join(run_dir, model_type)
            if os.path.exists(model_dir):
                # 获取模型信息
                model_info = self.get_model_info(model_type, run_dir)
                self.model_infos[model_type] = model_info

                # 填充可用模型路径
                if model_info["original"]:
                    self.model_paths[f"{model_type}-原始"] = {
                        "path": model_info["original"]["path"],
                        "type": model_type,
                        "variant": "original",
                        "device": self.device,
                    }

                if model_info["pruned"]:
                    self.model_paths[f"{model_type}-剪枝"] = {
                        "path": model_info["pruned"]["path"],
                        "type": model_type,
                        "variant": "pruned",
                        "device": self.device,
                    }

                if model_info["distilled"]:
                    self.model_paths[f"{model_type}-蒸馏"] = {
                        "path": model_info["distilled"]["path"],
                        "type": model_type,
                        "variant": "distilled",
                        "device": self.device,
                    }

                if model_info["quantized"]:
                    self.model_paths[f"{model_type}-量化"] = {
                        "path": model_info["quantized"]["path"],
                        "type": model_type,
                        "variant": "quantized",
                        "device": torch.device("cpu"),  # 量化模型只能在CPU上运行
                    }

        print(f"发现 {len(self.model_paths)} 个可用模型:")
        for name in self.model_paths:
            print(f" - {name}")

        return run_dir

    def load_model(self, model_key: str) -> bool:
        """
        按需加载指定的模型

        Args:
            model_key: 模型键名，格式为 "model_type-variant"

        Returns:
            加载是否成功
        """
        # 检查模型是否已经加载
        if model_key in self.models:
            print(f"模型 '{model_key}' 已加载，无需重新加载")
            return True

        # 检查模型信息是否存在
        if model_key not in self.model_paths:
            print(f"未找到模型 '{model_key}' 的信息")
            return False

        # 先清理所有已加载的模型，实现懒加载
        self.unload_all_models()

        model_info = self.model_paths[model_key]
        device_to_use = model_info["device"]
        model_type = model_info["type"]
        model_variant = model_info["variant"]
        model_path = model_info["path"]
        num_classes = len(self.classes)

        try:
            print(
                f"开始加载模型 '{model_key}' (类型: {model_type}, 变体: {model_variant})..."
            )

            # 根据模型变体加载
            if model_variant == "quantized":
                print(f"使用torch.jit.load加载量化模型: {model_path}")
                model = torch.jit.load(model_path)
                model.to(device_to_use).eval()

            elif model_variant == "distilled":
                print(f"创建MobileNetV2学生模型并加载权重: {model_path}")
                model = create_student_model(
                    "mobilenetv2", num_classes=num_classes, pretrained=False
                )
                model.load_state_dict(
                    torch.load(model_path, map_location=device_to_use)
                )
                model.to(device_to_use).eval()

            else:  # original, pruned
                print(f"创建 {model_type} 模型并加载权重: {model_path}")
                model = create_model(
                    model_name=model_type, num_classes=num_classes, pretrained=False
                )
                model.load_state_dict(
                    torch.load(model_path, map_location=device_to_use)
                )
                model.to(device_to_use).eval()

            # 保存到已加载模型字典
            self.models[model_key] = model
            print(f"成功加载模型 '{model_key}'")
            return True

        except Exception as e:
            print(f"加载模型 '{model_key}' 时出错: {e}")
            print("详细错误信息:")
            traceback.print_exc()
            return False

    def unload_all_models(self) -> None:
        """
        卸载所有已加载的模型以释放内存
        """
        if not self.models:
            return

        for model_key in list(self.models.keys()):
            try:
                del self.models[model_key]
            except Exception as e:
                print(f"卸载模型 '{model_key}' 时出错: {e}")

        self.models = {}

        # 清理 CUDA 缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        print("所有模型已卸载")

    def get_available_models(self) -> List[str]:
        """
        获取可用模型列表

        Returns:
            可用模型键名列表
        """
        return list(self.model_paths.keys())

    def get_model_info_dict(self) -> Dict:
        """
        获取模型信息字典

        Returns:
            模型信息字典
        """
        return self.model_infos

    def get_classes(self) -> List[str]:
        """
        获取类别列表

        Returns:
            类别列表
        """
        return self.classes

    def get_current_run_dir(self) -> str:
        """
        获取当前运行目录

        Returns:
            当前运行目录路径
        """
        return self.current_run_dir
