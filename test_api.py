#!/usr/bin/env python3
"""
API测试脚本

测试FastAPI应用的基本功能
"""

import requests
import json
import sys
import os
from PIL import Image
import io

def test_health_check(base_url):
    """测试健康检查端点"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查通过: {data['status']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_system_info(base_url):
    """测试系统信息端点"""
    print("🔍 测试系统信息...")
    try:
        response = requests.get(f"{base_url}/api/info")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                info = data['system_info']
                print(f"✅ 系统信息获取成功:")
                print(f"   - 设备: {info.get('device')}")
                print(f"   - CUDA可用: {info.get('cuda_available')}")
                print(f"   - PyTorch版本: {info.get('pytorch_version')}")
                return True
            else:
                print(f"❌ 系统信息获取失败")
                return False
        else:
            print(f"❌ 系统信息请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 系统信息异常: {e}")
        return False

def test_models_list(base_url):
    """测试模型列表端点"""
    print("🔍 测试模型列表...")
    try:
        response = requests.get(f"{base_url}/api/models/")
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 模型列表获取成功: 找到 {data['total_count']} 个模型")
                for model in data['models'][:3]:  # 显示前3个模型
                    print(f"   - {model['model_key']} ({model['model_type']}-{model['variant']})")
                return data['models']
            else:
                print(f"❌ 模型列表获取失败")
                return []
        else:
            print(f"❌ 模型列表请求失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 模型列表异常: {e}")
        return []

def create_test_image():
    """创建测试图像"""
    # 创建一个简单的RGB测试图像
    image = Image.new('RGB', (224, 224), color='blue')
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG')
    img_byte_arr.seek(0)
    return img_byte_arr

def test_prediction(base_url, models):
    """测试预测端点"""
    if not models:
        print("⚠️ 跳过预测测试: 没有可用模型")
        return False
    
    print("🔍 测试图像预测...")
    try:
        # 使用第一个可用模型
        model_key = models[0]['model_key']
        print(f"   使用模型: {model_key}")
        
        # 创建测试图像
        test_image = create_test_image()
        
        # 发送预测请求
        files = {'file': ('test.jpg', test_image, 'image/jpeg')}
        data = {'model_key': model_key}
        
        response = requests.post(f"{base_url}/api/predict/", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ 预测成功:")
                print(f"   - 推理时间: {result.get('inference_time', 'N/A')}")
                if result.get('results'):
                    top_class = max(result['results'], key=result['results'].get)
                    confidence = result['results'][top_class]
                    print(f"   - 预测结果: {top_class} (置信度: {confidence:.4f})")
                return True
            else:
                print(f"❌ 预测失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 预测请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 预测异常: {e}")
        return False

def main():
    """主测试函数"""
    base_url = "http://localhost:8000"
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🧪 开始测试API: {base_url}")
    print("=" * 50)
    
    # 测试计数
    tests_passed = 0
    total_tests = 4
    
    # 1. 健康检查
    if test_health_check(base_url):
        tests_passed += 1
    print()
    
    # 2. 系统信息
    if test_system_info(base_url):
        tests_passed += 1
    print()
    
    # 3. 模型列表
    models = test_models_list(base_url)
    if models:
        tests_passed += 1
    print()
    
    # 4. 预测测试
    if test_prediction(base_url, models):
        tests_passed += 1
    print()
    
    # 测试总结
    print("=" * 50)
    print(f"📊 测试完成: {tests_passed}/{total_tests} 通过")
    
    if tests_passed == total_tests:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查服务状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
