output_dir: outputs
data_dir: PatternNet/images
batch_size: 64
model_names: ['resnet50', 'densenet201', 'vit_s_16', 'swin_t']
pretrained: True
epochs: 50
learning_rate: 0.001
weight_decay: 0.0001
optimize_mode: all
pruning_methods: global
pruning_ratios: 0.5
fine_tune_epochs: 0
fine_tune_lr: 1e-05
student_model: mobilenetv2
temperature: 4.0
alpha: 0.7
distill_epochs: 25
distill_lr: 0.001
device: cuda
num_workers: 4
