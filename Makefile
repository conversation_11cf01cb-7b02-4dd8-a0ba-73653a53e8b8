# 遥感图像分类项目 Makefile

# 默认配置
IMAGE_NAME := remote-sensing-api
IMAGE_TAG := latest
CONTAINER_NAME := remote-sensing-api
HOST_PORT := 8000

# 颜色输出
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

.PHONY: help build run stop clean logs shell test dev gpu-build gpu-run install-deps

# 默认目标
help: ## 显示帮助信息
	@echo "遥感图像分类项目 - Docker管理命令"
	@echo ""
	@echo "可用命令:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(BLUE)%-15s$(NC) %s\n", $$1, $$2}'
	@echo ""
	@echo "示例:"
	@echo "  make build          # 构建Docker镜像"
	@echo "  make run            # 运行容器"
	@echo "  make dev            # 开发模式运行"
	@echo "  make gpu-run        # GPU模式运行"

build: ## 构建Docker镜像
	@echo "$(YELLOW)构建Docker镜像...$(NC)"
	@chmod +x docker-build.sh
	@./docker-build.sh -t $(IMAGE_TAG) -n $(IMAGE_NAME)

build-no-cache: ## 无缓存构建Docker镜像
	@echo "$(YELLOW)无缓存构建Docker镜像...$(NC)"
	@chmod +x docker-build.sh
	@./docker-build.sh -t $(IMAGE_TAG) -n $(IMAGE_NAME) --no-cache

gpu-build: ## 构建支持GPU的Docker镜像
	@echo "$(YELLOW)构建GPU支持的Docker镜像...$(NC)"
	@chmod +x docker-build.sh
	@./docker-build.sh -t $(IMAGE_TAG) -n $(IMAGE_NAME) -g

run: ## 运行容器
	@echo "$(YELLOW)启动容器...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -i $(IMAGE_NAME):$(IMAGE_TAG) -n $(CONTAINER_NAME) -p $(HOST_PORT)

run-detached: ## 后台运行容器
	@echo "$(YELLOW)后台启动容器...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -i $(IMAGE_NAME):$(IMAGE_TAG) -n $(CONTAINER_NAME) -p $(HOST_PORT) -d

gpu-run: ## GPU模式运行容器
	@echo "$(YELLOW)GPU模式启动容器...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -i $(IMAGE_NAME):$(IMAGE_TAG) -n $(CONTAINER_NAME) -p $(HOST_PORT) -g

dev: ## 开发模式运行
	@echo "$(YELLOW)开发模式启动...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -i $(IMAGE_NAME):$(IMAGE_TAG) -n $(CONTAINER_NAME)-dev -p 8001 --dev

stop: ## 停止容器
	@echo "$(YELLOW)停止容器...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -n $(CONTAINER_NAME) --stop

logs: ## 查看容器日志
	@echo "$(BLUE)查看容器日志...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -n $(CONTAINER_NAME) --logs

shell: ## 进入容器shell
	@echo "$(BLUE)进入容器shell...$(NC)"
	@chmod +x docker-run.sh
	@./docker-run.sh -n $(CONTAINER_NAME) --shell

clean: ## 清理容器和镜像
	@echo "$(YELLOW)清理容器和镜像...$(NC)"
	@docker ps -a -q -f name=$(CONTAINER_NAME) | xargs -r docker rm -f
	@docker images -q $(IMAGE_NAME) | xargs -r docker rmi -f
	@echo "$(GREEN)清理完成$(NC)"

test: ## 运行API测试
	@echo "$(BLUE)运行API测试...$(NC)"
	@python3 test_api.py

install-deps: ## 安装Python依赖
	@echo "$(YELLOW)安装Python依赖...$(NC)"
	@pip install -r requirements.txt

# Docker Compose相关命令
compose-up: ## 使用Docker Compose启动
	@echo "$(YELLOW)使用Docker Compose启动...$(NC)"
	@docker-compose up

compose-up-detached: ## 使用Docker Compose后台启动
	@echo "$(YELLOW)使用Docker Compose后台启动...$(NC)"
	@docker-compose up -d

compose-gpu: ## 使用Docker Compose GPU模式启动
	@echo "$(YELLOW)使用Docker Compose GPU模式启动...$(NC)"
	@docker-compose -f docker-compose.gpu.yml up

compose-dev: ## 使用Docker Compose开发模式启动
	@echo "$(YELLOW)使用Docker Compose开发模式启动...$(NC)"
	@docker-compose --profile dev up remote-sensing-dev

compose-down: ## 停止Docker Compose服务
	@echo "$(YELLOW)停止Docker Compose服务...$(NC)"
	@docker-compose down

compose-logs: ## 查看Docker Compose日志
	@echo "$(BLUE)查看Docker Compose日志...$(NC)"
	@docker-compose logs -f

# 健康检查
health: ## 检查服务健康状态
	@echo "$(BLUE)检查服务健康状态...$(NC)"
	@curl -f http://localhost:$(HOST_PORT)/health || echo "$(RED)服务不可用$(NC)"

# 快速启动命令
quick-start: build run ## 快速构建并启动
	@echo "$(GREEN)快速启动完成!$(NC)"
	@echo "$(BLUE)访问地址: http://localhost:$(HOST_PORT)$(NC)"
	@echo "$(BLUE)API文档: http://localhost:$(HOST_PORT)/docs$(NC)"

# 完整重建
rebuild: clean build ## 完整重建镜像
	@echo "$(GREEN)重建完成!$(NC)"

# 显示状态
status: ## 显示容器状态
	@echo "$(BLUE)容器状态:$(NC)"
	@docker ps -a -f name=$(CONTAINER_NAME)
	@echo ""
	@echo "$(BLUE)镜像信息:$(NC)"
	@docker images $(IMAGE_NAME)

# 备份和恢复
backup: ## 备份容器数据
	@echo "$(YELLOW)备份容器数据...$(NC)"
	@mkdir -p backup
	@docker cp $(CONTAINER_NAME):/app/outputs backup/ 2>/dev/null || echo "$(YELLOW)outputs目录不存在或容器未运行$(NC)"
	@docker cp $(CONTAINER_NAME):/app/imgs backup/ 2>/dev/null || echo "$(YELLOW)imgs目录不存在或容器未运行$(NC)"
	@echo "$(GREEN)备份完成$(NC)"
