# 项目特定文件
dependencies/
PatternNet.zip

# Git相关
.git/
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
test_*.html

# 文档
docs/
README*

# 开发工具
.env
.env.local
.env.development
.env.test
.env.production

# Docker相关
Dockerfile.dev
docker-compose.override.yml
.dockerignore.bak

# 其他
*.bak
*.orig