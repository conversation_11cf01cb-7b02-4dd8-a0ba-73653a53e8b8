from fastapi import APIRouter, HTTPException, Depends
from typing import List
import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from api.schemas.models import ModelsListResponse, ModelInfo, ComparisonResponse, ErrorResponse
from api.services.model_service import ModelService
from api.services.comparison_service import ComparisonService

router = APIRouter(prefix="/models", tags=["models"])

# 全局服务实例
model_service = ModelService()
comparison_service = ComparisonService(model_service)


def get_model_service():
    """依赖注入：获取模型服务实例"""
    return model_service


def get_comparison_service():
    """依赖注入：获取比较服务实例"""
    return comparison_service


@router.get("/", response_model=ModelsListResponse)
async def get_available_models(
    model_svc: ModelService = Depends(get_model_service)
):
    """
    获取可用模型列表
    
    Returns:
        可用模型列表和相关信息
    """
    try:
        # 发现可用模型
        run_dir = model_svc.discover_available_models()
        
        if run_dir is None:
            return ModelsListResponse(
                success=False,
                models=[],
                total_count=0,
                current_run_dir=None
            )
        
        # 获取模型列表
        available_models = model_svc.get_available_models()
        
        # 构建模型信息列表
        model_infos = []
        for model_key in available_models:
            model_path_info = model_svc.model_paths.get(model_key, {})
            model_info = ModelInfo(
                model_key=model_key,
                model_type=model_path_info.get("type", "unknown"),
                variant=model_path_info.get("variant", "unknown"),
                device=str(model_path_info.get("device", "unknown")),
                available=True
            )
            model_infos.append(model_info)
        
        return ModelsListResponse(
            success=True,
            models=model_infos,
            total_count=len(model_infos),
            current_run_dir=run_dir
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取模型列表失败: {str(e)}"
        )


@router.get("/{model_type}/comparison/{comparison_type}", response_model=ComparisonResponse)
async def get_model_comparison(
    model_type: str,
    comparison_type: str,
    comparison_svc: ComparisonService = Depends(get_comparison_service)
):
    """
    获取模型比较结果
    
    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
        comparison_type: 比较类型 (pruning, distillation, quantization)
    
    Returns:
        模型比较的HTML内容
    """
    try:
        # 验证模型类型
        valid_model_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]
        if model_type not in valid_model_types:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模型类型。支持的类型: {', '.join(valid_model_types)}"
            )
        
        # 验证比较类型
        valid_comparison_types = ["pruning", "distillation", "quantization"]
        if comparison_type not in valid_comparison_types:
            raise HTTPException(
                status_code=400,
                detail=f"无效的比较类型。支持的类型: {', '.join(valid_comparison_types)}"
            )
        
        # 量化比较仅支持ResNet50
        if comparison_type == "quantization" and model_type != "resnet50":
            raise HTTPException(
                status_code=400,
                detail="量化模型比较仅支持ResNet50"
            )
        
        # 调用相应的比较方法
        if comparison_type == "pruning":
            html_content = comparison_svc.create_pruning_comparison(model_type)
        elif comparison_type == "distillation":
            html_content = comparison_svc.create_distillation_comparison(model_type)
        elif comparison_type == "quantization":
            html_content = comparison_svc.create_quantization_comparison(model_type)
        
        # 检查是否返回错误信息
        if html_content.startswith("未找到") or html_content.startswith("量化模型比较仅支持"):
            return ComparisonResponse(
                success=False,
                html_content=None,
                error=html_content
            )
        
        return ComparisonResponse(
            success=True,
            html_content=html_content,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"生成模型比较失败: {str(e)}"
        )
