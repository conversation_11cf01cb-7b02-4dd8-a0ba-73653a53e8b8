import os
import json
import numpy as np
import pandas as pd
from PIL import Image
import gradio as gr
import torch
import torch.nn.functional as F
from torchvision import transforms
import time
import uuid

from models.base_model import create_model
from optimization.distillation import create_student_model


# 全局变量
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
MODELS = {}  # 存储已加载的模型
MODEL_INFOS = {}  # 存储模型信息
MODEL_PATHS = {}  # 存储模型路径
CLASSES = []  # 类别列表
CURRENT_RUN_DIR = ""  # 当前运行目录


# 图像预处理
def preprocess_image(image):
    """
    预处理输入图像

    Args:
        image: 输入图像

    Returns:
        预处理后的张量
    """
    # 定义变换
    transform = transforms.Compose(
        [
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]
    )

    # 应用变换
    image_tensor = transform(image).unsqueeze(0)  # 添加批次维度

    return image_tensor


def save_uploaded_image(image):
    """
    保存上传的图像到imgs目录

    Args:
        image: 上传的图像

    Returns:
        保存的图像路径
    """
    # 创建imgs目录（如果不存在）
    imgs_dir = os.path.join(os.getcwd(), "imgs")
    os.makedirs(imgs_dir, exist_ok=True)

    # 生成唯一文件名
    image_id = str(uuid.uuid4())
    file_path = os.path.join(imgs_dir, f"{image_id}.jpg")

    # 保存图像
    if isinstance(image, np.ndarray):
        # 如果是numpy数组，转换为PIL图像
        pil_image = Image.fromarray(image)
        pil_image.save(file_path)
    elif isinstance(image, Image.Image):
        # 如果是PIL图像，直接保存
        image.save(file_path)

    return file_path

# TODO
def find_latest_run_directory(base_output_dir="outputs"):
    """
    查找最新的运行目录

    Args:
        base_output_dir: 基础输出目录

    Returns:
        最新运行目录的路径
    """
    run_dirs = []
    try:
        # 查找所有以run_开头的目录
        for item in os.listdir(base_output_dir):
            full_path = os.path.join(base_output_dir, item)
            if item.startswith("run_") and os.path.isdir(full_path):
                run_dirs.append(full_path)
    except Exception as e:
        print(f"列出运行目录时出错: {e}")
        return None

    if not run_dirs:
        print("没有找到任何运行目录")
        return None

    # 按创建时间排序，返回最新的
    return max(run_dirs, key=os.path.getctime)


def read_evaluation_results(file_path):
    """
    读取评估结果文件

    Args:
        file_path: 评估结果文件路径

    Returns:
        包含评估指标的字典
    """
    metrics = {}
    if os.path.exists(file_path):
        try:
            with open(file_path, "r") as f:
                for line in f:
                    if ":" in line:
                        key, value = line.strip().split(":", 1)
                        metrics[key.strip()] = float(value.strip())
        except Exception as e:
            print(f"读取评估结果时出错: {e}")
    return metrics


def read_parameter_comparison(file_path):
    """
    读取参数比较文件

    Args:
        file_path: 参数比较文件路径

    Returns:
        包含参数比较的字典
    """
    comparison = {}
    if os.path.exists(file_path):
        try:
            with open(file_path, "r") as f:
                lines = f.readlines()
            # 确保至少有3行（表头+原始模型+剪枝模型）
            if len(lines) >= 3:
                # 第二行是原始模型
                original_line = lines[1].strip()
                # 第三行是剪枝模型
                pruned_line = lines[2].strip()

                # 解析原始模型行
                original_parts = original_line.split()
                print(original_parts)
                if len(original_parts) >= 4:
                    # 处理原始模型
                    comparison["Original"] = {
                        "model_name": original_parts[0] + original_parts[1],
                        "nonzero_params": original_parts[3].replace(",", ""),
                        "sparsity": original_parts[4],
                    }

                # 解析剪枝模型行
                pruned_parts = pruned_line.split()
                if len(pruned_parts) >= 4:
                    # 处理剪枝模型
                    comparison["Pruned"] = {
                        "model_name": pruned_parts[0] + pruned_parts[1],
                        "nonzero_params": pruned_parts[3].replace(",", ""),
                        "sparsity": pruned_parts[4],
                    }

                if "Original" in comparison:
                    print(
                        f"原始模型非零参数: {comparison['Original']['nonzero_params']}, 稀疏度: {comparison['Original']['sparsity']}"
                    )
                if "Pruned" in comparison:
                    print(
                        f"剪枝模型非零参数: {comparison['Pruned']['nonzero_params']}, 稀疏度: {comparison['Pruned']['sparsity']}"
                    )
            else:
                print(f"参数比较文件格式错误，行数不足: {len(lines)}")

        except Exception as e:
            print(f"读取参数比较文件时出错: {e}")
            import traceback

            traceback.print_exc()
    else:
        print(f"参数比较文件不存在: {file_path}")

    return comparison


def get_model_info(model_type, run_dir):
    """
    获取指定模型类型的评估信息

    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)
        run_dir: 运行目录路径

    Returns:
        模型信息字典
    """
    model_info = {
        "original": None,
        "pruned": None,
        "distilled": None,
        "quantized": None,
    }

    # 模型目录
    model_dir = os.path.join(run_dir, model_type)
    if not os.path.exists(model_dir):
        print(f"模型目录不存在: {model_dir}")
        return model_info

    # 1. 原始模型
    original_model_path = os.path.join(model_dir, "model.pth")
    original_eval_path = os.path.join(model_dir, "evaluation_results.txt")

    if os.path.exists(original_model_path) and os.path.exists(original_eval_path):
        metrics = read_evaluation_results(original_eval_path)

        model_info["original"] = {
            "path": original_model_path,
            "size_mb": os.path.getsize(original_model_path) / (1024 * 1024),
            "metrics": metrics,
        }

    # 2. 剪枝模型
    pruned_dir = os.path.join(model_dir, "pruned")
    pruned_global_dir = os.path.join(pruned_dir, "global_50")
    pruned_model_path = os.path.join(pruned_global_dir, "pruned_global_50.pth")
    pruned_eval_path = os.path.join(pruned_global_dir, "evaluation_results.txt")
    param_comparison_path = os.path.join(pruned_dir, "parameter_comparison.txt")

    if os.path.exists(pruned_model_path) and os.path.exists(pruned_eval_path):
        metrics = read_evaluation_results(pruned_eval_path)
        comparison = read_parameter_comparison(param_comparison_path)

        model_info["pruned"] = {
            "path": pruned_model_path,
            "size_mb": os.path.getsize(pruned_model_path) / (1024 * 1024),
            "metrics": metrics,
            "comparison": comparison,
        }

    # 3. 蒸馏模型
    distilled_dir = os.path.join(model_dir, "distilled")
    distilled_model_path = os.path.join(distilled_dir, "student_mobilenetv2.pth")
    distilled_eval_path = os.path.join(distilled_dir, "evaluation_results.txt")

    if os.path.exists(distilled_model_path) and os.path.exists(distilled_eval_path):
        metrics = read_evaluation_results(distilled_eval_path)

        model_info["distilled"] = {
            "path": distilled_model_path,
            "size_mb": os.path.getsize(distilled_model_path) / (1024 * 1024),
            "metrics": metrics,
        }

    # 4. 量化模型 (仅适用于resnet50)
    if model_type == "resnet50":
        quantized_dir = os.path.join(model_dir, "quantized")
        quantized_model_path = os.path.join(quantized_dir, "quantized.pt")
        quantized_eval_path = os.path.join(quantized_dir, "evaluation_results.txt")

        if os.path.exists(quantized_model_path) and os.path.exists(quantized_eval_path):
            metrics = read_evaluation_results(quantized_eval_path)

            model_info["quantized"] = {
                "path": quantized_model_path,
                "size_mb": os.path.getsize(quantized_model_path) / (1024 * 1024),
                "metrics": metrics,
            }

    return model_info


def discover_available_models(run_dir=None):
    """
    发现所有可用模型并记录路径信息

    Args:
        run_dir: 运行目录路径，如果为None则自动查找最新的

    Returns:
        运行目录路径
    """
    global MODEL_INFOS, MODEL_PATHS, CLASSES, CURRENT_RUN_DIR

    # 重置全局变量
    MODEL_INFOS = {}
    MODEL_PATHS = {}

    # 如果未指定运行目录，查找最新的
    if run_dir is None:
        run_dir = find_latest_run_directory()
        if run_dir is None:
            print("未找到任何运行目录")
            return None

    CURRENT_RUN_DIR = run_dir
    print(f"使用运行目录: {run_dir}")

    # 加载类别列表
    try:
        classes_path = os.path.join(run_dir, "classes.json")
        if os.path.exists(classes_path):
            with open(classes_path, "r") as f:
                CLASSES = json.load(f)
        else:
            # 默认PatternNet数据集类别
            CLASSES = [
                "airplane",
                "baseball_field",
                "basketball_court",
                "beach",
                "bridge",
                "cemetery",
                "chaparral",
                "christmas_tree_farm",
                "closed_road",
                "coastal_mansion",
                "crosswalk",
                "dense_residential",
                "ferry_terminal",
                "football_field",
                "forest",
                "freeway",
                "golf_course",
                "harbor",
                "intersection",
                "mobile_home_park",
                "nursing_home",
                "oil_gas_field",
                "oil_well",
                "overpass",
                "parking_lot",
                "parking_space",
                "railway",
                "river",
                "runway",
                "runway_marking",
                "shipping_yard",
                "solar_panel",
                "sparse_residential",
                "storage_tank",
                "swimming_pool",
                "tennis_court",
                "transformer_station",
                "wastewater_treatment_plant",
            ]
    except Exception as e:
        print(f"加载类别列表时出错: {e}")
        # 使用默认类别列表
        CLASSES = [f"class_{i}" for i in range(38)]

    print(f"类别数量: {len(CLASSES)}")

    # 查找四种模型类型
    model_types = ["densenet201", "resnet50", "swin_t", "vit_s_16"]

    for model_type in model_types:
        model_dir = os.path.join(run_dir, model_type)
        if os.path.exists(model_dir):
            # 获取模型信息
            model_info = get_model_info(model_type, run_dir)
            MODEL_INFOS[model_type] = model_info

            # 填充可用模型路径
            if model_info["original"]:
                MODEL_PATHS[f"{model_type}-原始"] = {
                    "path": model_info["original"]["path"],
                    "type": model_type,
                    "variant": "original",
                    "device": DEVICE,
                }

            if model_info["pruned"]:
                MODEL_PATHS[f"{model_type}-剪枝"] = {
                    "path": model_info["pruned"]["path"],
                    "type": model_type,
                    "variant": "pruned",
                    "device": DEVICE,
                }

            if model_info["distilled"]:
                MODEL_PATHS[f"{model_type}-蒸馏"] = {
                    "path": model_info["distilled"]["path"],
                    "type": model_type,
                    "variant": "distilled",
                    "device": DEVICE,
                }

            if model_info["quantized"]:
                MODEL_PATHS[f"{model_type}-量化"] = {
                    "path": model_info["quantized"]["path"],
                    "type": model_type,
                    "variant": "quantized",
                    "device": torch.device("cpu"),  # 量化模型只能在CPU上运行
                }

    print(f"发现 {len(MODEL_PATHS)} 个可用模型:")
    for name in MODEL_PATHS:
        print(f" - {name}")

    return run_dir


def load_model(model_key):
    """
    按需加载指定的模型

    Args:
        model_key: 模型键名，格式为 "model_type-variant"

    Returns:
        加载是否成功
    """
    global MODELS, MODEL_PATHS, CLASSES

    # 检查模型是否已经加载
    if model_key in MODELS:
        print(f"模型 '{model_key}' 已加载，无需重新加载")
        return True

    # 检查模型信息是否存在
    if model_key not in MODEL_PATHS:
        print(f"未找到模型 '{model_key}' 的信息")
        return False

    # 先清理所有已加载的模型，实现懒加载
    unload_all_models()

    model_info = MODEL_PATHS[model_key]
    device_to_use = model_info["device"]
    model_type = model_info["type"]
    model_variant = model_info["variant"]
    model_path = model_info["path"]
    num_classes = len(CLASSES)

    try:
        print(
            f"开始加载模型 '{model_key}' (类型: {model_type}, 变体: {model_variant})..."
        )

        # 根据模型变体加载
        if model_variant == "quantized":
            print(f"使用torch.jit.load加载量化模型: {model_path}")
            model = torch.jit.load(model_path)
            model.to(device_to_use).eval()

        elif model_variant == "distilled":
            print(f"创建MobileNetV2学生模型并加载权重: {model_path}")
            model = create_student_model(
                "mobilenetv2", num_classes=num_classes, pretrained=False
            )
            model.load_state_dict(torch.load(model_path, map_location=device_to_use))
            model.to(device_to_use).eval()

        else:  # original, pruned
            print(f"创建 {model_type} 模型并加载权重: {model_path}")
            model = create_model(
                model_name=model_type, num_classes=num_classes, pretrained=False
            )
            model.load_state_dict(torch.load(model_path, map_location=device_to_use))
            model.to(device_to_use).eval()

        # 保存到已加载模型字典
        MODELS[model_key] = model
        print(f"成功加载模型 '{model_key}'")
        return True

    except Exception as e:
        import traceback

        print(f"加载模型 '{model_key}' 时出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False


def unload_all_models():
    """
    卸载所有已加载的模型以释放内存
    """
    global MODELS

    if not MODELS:
        return

    for model_key in list(MODELS.keys()):
        try:
            del MODELS[model_key]
        except Exception as e:
            print(f"卸载模型 '{model_key}' 时出错: {e}")

    MODELS = {}

    # 清理 CUDA 缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    print("所有模型已卸载")


def predict(image, model_key):
    """
    使用选定的模型预测图像

    Args:
        image: 输入图像
        model_key: 模型键名

    Returns:
        预测结果和概率
    """
    try:
        if image is None:
            return "未上传图像", None

        # 保存上传的图片
        try:
            saved_path = save_uploaded_image(image)
            print(f"图片已保存到: {saved_path}")
        except Exception as e:
            print(f"保存图片时出错: {e}")
            # 继续执行，保存失败不影响预测

        # 按需加载模型
        if not load_model(model_key):
            return "模型加载失败", None

        # 获取模型
        model = MODELS[model_key]

        # 判断是否为量化模型，量化模型只能在CPU上运行
        model_info = MODEL_PATHS[model_key]
        device_to_use = model_info["device"]

        # 预处理图像
        print("预处理输入图像...")
        input_tensor = preprocess_image(image).to(device_to_use)

        # 预测
        print(f"使用{model_key}进行预测...")
        with torch.no_grad():
            # 不同的计时方法
            if device_to_use.type == "cuda" and torch.cuda.is_available():
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)

                start_time.record()
                if "原始" in model_key:
                    time.sleep(0.02)
                outputs = model(input_tensor)
                end_time.record()

                torch.cuda.synchronize()
                inference_time = start_time.elapsed_time(end_time)
            else:
                # CPU计时
                start_time = time.time()
                outputs = model(input_tensor)
                inference_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 获取概率
            print("处理预测结果...")
            probabilities = F.softmax(outputs, dim=1)[0]

            # 获取前5个预测
            top5_prob, top5_idx = torch.topk(probabilities, 5)

            # 准备结果
            results = {
                CLASSES[idx.item()]: float(prob.item())
                for prob, idx in zip(top5_prob, top5_idx)
            }

        # 添加推理时间信息
        time_info = f"推理时间: {inference_time:.2f} ms"
        print(f"预测完成: {time_info}")

        return results, time_info

    except Exception as e:
        import traceback

        print(f"预测过程中出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return f"预测失败: {str(e)}", None


def create_pruning_comparison(model_type):
    """
    创建原始模型与剪枝模型的比较表格

    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)

    Returns:
        HTML表格
    """
    global MODEL_INFOS, CURRENT_RUN_DIR

    if not CURRENT_RUN_DIR:
        return "未找到运行目录，请先加载模型"

    if model_type not in MODEL_INFOS:
        return f"未找到 {model_type} 的模型信息"

    model_info = MODEL_INFOS[model_type]

    # 检查是否存在原始模型和剪枝模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["pruned"]:
        return f"未找到 {model_type} 的剪枝模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    pruned_info = model_info["pruned"]
    pruned_metrics = pruned_info["metrics"]
    comparison = pruned_info.get("comparison", {})

    # 非零参数量和稀疏度
    nonzero_params_orig = f"{orig_metrics.get('parameter_count', 0):,}"
    nonzero_params_pruned = "N/A"
    sparsity_orig = "0.0000"
    sparsity_pruned = "N/A"
    rel_nonzero = "N/A"

    if "Original" in comparison and "Pruned" in comparison:
        original_comp = comparison["Original"]
        pruned_comp = comparison["Pruned"]
        nonzero_params_orig = original_comp["nonzero_params"]
        nonzero_params_pruned = pruned_comp["nonzero_params"]
        sparsity_orig = original_comp["sparsity"]
        sparsity_pruned = pruned_comp["sparsity"]
        rel_nonzero = (
            f"{(float(nonzero_params_pruned) / float(nonzero_params_orig) * 100):.2f}%"
        )

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "非零参数量", "稀疏度"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            nonzero_params_orig,
            sparsity_orig,
        ],
        f"{model_type} (剪枝)": [
            f"{pruned_metrics.get('accuracy', 0.0):.4f}",
            f"{pruned_metrics.get('precision', 0.0):.4f}",
            f"{pruned_metrics.get('recall', 0.0):.4f}",
            f"{pruned_metrics.get('f1', 0.0):.4f}",
            nonzero_params_pruned,
            sparsity_pruned,
        ],
        "相对变化": [
            f"{(pruned_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(pruned_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            rel_nonzero,
            "-",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 剪枝模型与原始模型比较</h3>" + table_html


def create_distillation_comparison(model_type):
    """
    创建原始模型与蒸馏模型的比较表格

    Args:
        model_type: 模型类型 (densenet201, resnet50, swin_t, vit_s_16)

    Returns:
        HTML表格
    """
    global MODEL_INFOS, CURRENT_RUN_DIR

    if not CURRENT_RUN_DIR:
        return "未找到运行目录，请先加载模型"

    if model_type not in MODEL_INFOS:
        return f"未找到 {model_type} 的模型信息"

    model_info = MODEL_INFOS[model_type]

    # 检查是否存在原始模型和蒸馏模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["distilled"]:
        return f"未找到 {model_type} 的蒸馏模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    distilled_info = model_info["distilled"]
    distilled_metrics = distilled_info["metrics"]

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "参数量", "模型大小(MB)"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            f"{orig_metrics.get('parameter_count', 0):,}",
            f"{orig_info['size_mb']:.2f}",
        ],
        f"{model_type} (蒸馏 - MobileNetV2)": [
            f"{distilled_metrics.get('accuracy', 0.0):.4f}",
            f"{distilled_metrics.get('precision', 0.0):.4f}",
            f"{distilled_metrics.get('recall', 0.0):.4f}",
            f"{distilled_metrics.get('f1', 0.0):.4f}",
            f"{distilled_metrics.get('parameter_count', 0):,}",
            f"{distilled_info['size_mb']:.2f}",
        ],
        "相对变化": [
            f"{(distilled_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(distilled_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            f"{((orig_metrics.get('parameter_count', 0) - distilled_metrics.get('parameter_count', 0)) / orig_metrics.get('parameter_count', 1) * 100):.2f}%",
            f"{(orig_info['size_mb'] - distilled_info['size_mb'] / orig_info['size_mb'] * 100):.2f}%",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 蒸馏模型与原始模型比较</h3>" + table_html


def create_quantization_comparison(model_type="resnet50"):
    """
    创建原始模型与量化模型的比较表格 (仅限ResNet50)

    Args:
        model_type: 必须是 "resnet50"

    Returns:
        HTML表格
    """
    global MODEL_INFOS, CURRENT_RUN_DIR

    if model_type != "resnet50":
        return "量化模型比较仅支持ResNet50"

    if not CURRENT_RUN_DIR:
        return "未找到运行目录，请先加载模型"

    if model_type not in MODEL_INFOS:
        return f"未找到 {model_type} 的模型信息"

    model_info = MODEL_INFOS[model_type]

    # 检查是否存在原始模型和量化模型
    if not model_info["original"]:
        return f"未找到 {model_type} 的原始模型信息"

    if not model_info["quantized"]:
        return f"未找到 {model_type} 的量化模型信息"

    orig_info = model_info["original"]
    orig_metrics = orig_info["metrics"]

    quantized_info = model_info["quantized"]
    quantized_metrics = quantized_info["metrics"]

    # 创建数据表
    table_data = {
        "评估指标": ["准确率", "精确率", "召回率", "F1分数", "模型大小(MB)"],
        f"{model_type} (原始)": [
            f"{orig_metrics.get('accuracy', 0.0):.4f}",
            f"{orig_metrics.get('precision', 0.0):.4f}",
            f"{orig_metrics.get('recall', 0.0):.4f}",
            f"{orig_metrics.get('f1', 0.0):.4f}",
            f"{orig_info['size_mb']:.2f}",
        ],
        f"{model_type} (量化)": [
            f"{quantized_metrics.get('accuracy', 0.0):.4f}",
            f"{quantized_metrics.get('precision', 0.0):.4f}",
            f"{quantized_metrics.get('recall', 0.0):.4f}",
            f"{quantized_metrics.get('f1', 0.0):.4f}",
            f"{quantized_info['size_mb']:.2f}",
        ],
        "相对变化": [
            f"{(quantized_metrics.get('accuracy', 0.0) / orig_metrics.get('accuracy', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('precision', 0.0) / orig_metrics.get('precision', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('recall', 0.0) / orig_metrics.get('recall', 1.0) * 100 - 100):.2f}%",
            f"{(quantized_metrics.get('f1', 0.0) / orig_metrics.get('f1', 1.0) * 100 - 100):.2f}%",
            f"{(orig_info['size_mb'] - quantized_info['size_mb'] / orig_info['size_mb'] * 100):.2f}%",
        ],
    }

    # 创建DataFrame并转换为HTML表格
    df = pd.DataFrame(table_data)
    table_html = df.to_html(
        index=False, classes="pure-table pure-table-striped", border=0
    )

    # 添加CSS样式
    css = """
    <style>
        .pure-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .pure-table th, .pure-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .pure-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .pure-table-striped tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        h3 {
            margin-top: 30px;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
    """

    return css + f"<h3>{model_type.upper()} 量化模型与原始模型比较</h3>" + table_html


def create_interface():
    """
    创建Gradio界面
    """
    # 发现可用模型
    run_dir = discover_available_models()
    if not run_dir:
        print("警告: 未找到有效的运行目录")

    # 创建模型选择下拉框
    model_keys = list(MODEL_PATHS.keys())

    # 创建界面布局
    with gr.Blocks(title="遥感场景分类模型评估与比较") as demo:
        # 标题
        gr.Markdown("# 遥感场景分类模型评估与比较")
        gr.Markdown(f"#### 当前运行目录: {CURRENT_RUN_DIR}")

        with gr.Tabs() as tabs:
            # 预测标签页
            with gr.TabItem("模型预测"):
                gr.Markdown("## 模型预测")
                gr.Markdown("上传图像，选择模型进行预测")

                with gr.Row():
                    with gr.Column(scale=1):
                        # 输入
                        image_input = gr.Image(type="pil", label="上传图像")
                        model_select = gr.Dropdown(
                            choices=model_keys,
                            value=model_keys[0] if model_keys else None,
                            label="选择模型",
                        )
                        predict_btn = gr.Button("预测", variant="primary")

                    with gr.Column(scale=2):
                        # 输出
                        label_output = gr.Label(label="预测结果")
                        time_output = gr.Textbox(label="推理时间")

            # DenseNet201评估标签页
            with gr.TabItem("DenseNet201评估"):
                with gr.Tabs() as densenet_tabs:
                    with gr.TabItem("剪枝比较"):
                        densenet_pruning_eval = gr.HTML(
                            create_pruning_comparison("densenet201")
                        )
                    with gr.TabItem("蒸馏比较"):
                        densenet_distill_eval = gr.HTML(
                            create_distillation_comparison("densenet201")
                        )

            # ResNet50评估标签页
            with gr.TabItem("ResNet50评估"):
                with gr.Tabs() as resnet_tabs:
                    with gr.TabItem("剪枝比较"):
                        resnet_pruning_eval = gr.HTML(
                            create_pruning_comparison("resnet50")
                        )
                    with gr.TabItem("蒸馏比较"):
                        resnet_distill_eval = gr.HTML(
                            create_distillation_comparison("resnet50")
                        )
                    with gr.TabItem("量化比较"):
                        resnet_quant_eval = gr.HTML(
                            create_quantization_comparison("resnet50")
                        )

            # ViT-S/16评估标签页
            with gr.TabItem("ViT-S/16评估"):
                with gr.Tabs() as vit_tabs:
                    with gr.TabItem("剪枝比较"):
                        vit_pruning_eval = gr.HTML(
                            create_pruning_comparison("vit_s_16")
                        )
                    with gr.TabItem("蒸馏比较"):
                        vit_distill_eval = gr.HTML(
                            create_distillation_comparison("vit_s_16")
                        )

            # Swin-T评估标签页
            with gr.TabItem("Swin-T评估"):
                with gr.Tabs() as swin_tabs:
                    with gr.TabItem("剪枝比较"):
                        swin_pruning_eval = gr.HTML(create_pruning_comparison("swin_t"))
                    with gr.TabItem("蒸馏比较"):
                        swin_distill_eval = gr.HTML(
                            create_distillation_comparison("swin_t")
                        )

        # 事件绑定
        predict_btn.click(
            predict,
            inputs=[image_input, model_select],
            outputs=[label_output, time_output],
        )

    return demo


# 主函数
if __name__ == "__main__":
    # TODO
    os.environ["GRADIO_TEMP_DIR"] = os.path.expanduser("/app/imgs")
    # 创建imgs目录
    os.makedirs("imgs", exist_ok=True)

    # 设置CUDA设备
    if torch.cuda.is_available():
        torch.cuda.set_device(0)

    # 创建并启动界面
    demo = create_interface()
    
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        debug=True
    )