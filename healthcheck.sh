#!/bin/bash

# Docker健康检查脚本
# 用于容器内部健康检查

set -e

# 配置
HEALTH_CHECK_URL="http://localhost:8000/health"
TIMEOUT=10
MAX_RETRIES=3

# 颜色输出（在容器内可能不显示颜色）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 检查curl是否可用
if ! command -v curl &> /dev/null; then
    log "错误: curl命令不可用"
    exit 1
fi

# 执行健康检查
for i in $(seq 1 $MAX_RETRIES); do
    log "执行健康检查 (尝试 $i/$MAX_RETRIES)..."
    
    if curl -f -s --max-time $TIMEOUT "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
        log "✅ 健康检查通过"
        
        # 获取详细健康信息
        HEALTH_INFO=$(curl -s --max-time $TIMEOUT "$HEALTH_CHECK_URL" 2>/dev/null || echo '{}')
        log "健康信息: $HEALTH_INFO"
        
        exit 0
    else
        log "❌ 健康检查失败 (尝试 $i/$MAX_RETRIES)"
        
        if [ $i -lt $MAX_RETRIES ]; then
            log "等待2秒后重试..."
            sleep 2
        fi
    fi
done

log "❌ 健康检查最终失败，已达到最大重试次数"
exit 1
