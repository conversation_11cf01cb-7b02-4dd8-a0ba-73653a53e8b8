// 遥感图像分类前端应用
class RemoteSensingApp {
    constructor() {
        this.apiBase = '/api';
        this.selectedFile = null;
        this.availableModels = [];
        
        this.init();
    }

    // 初始化应用
    async init() {
        this.setupEventListeners();
        await this.loadModels();
        this.setupDragAndDrop();
    }

    // 设置事件监听器
    setupEventListeners() {
        // 图像输入
        document.getElementById('imageInput').addEventListener('change', (e) => {
            this.handleImageSelect(e.target.files[0]);
        });

        // 预测按钮
        document.getElementById('predictButton').addEventListener('click', () => {
            this.performPrediction();
        });

        // 比较按钮
        document.getElementById('compareButton').addEventListener('click', () => {
            this.performComparison();
        });

        // 模型选择变化
        document.getElementById('modelSelect').addEventListener('change', () => {
            this.updatePredictButton();
        });

        // 比较类型变化
        document.getElementById('comparisonTypeSelect').addEventListener('change', () => {
            this.updateQuantizationAvailability();
        });

        // 模型类型变化
        document.getElementById('modelTypeSelect').addEventListener('change', () => {
            this.updateQuantizationAvailability();
        });
    }

    // 设置拖拽上传
    setupDragAndDrop() {
        const uploadArea = document.querySelector('.upload-area');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('dragover');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageSelect(files[0]);
            }
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 加载可用模型
    async loadModels() {
        try {
            const response = await fetch(`${this.apiBase}/models/`);
            const data = await response.json();
            
            if (data.success) {
                this.availableModels = data.models;
                this.populateModelSelect();
                this.showMessage('predictionMessage', `成功加载 ${data.total_count} 个模型`, 'success');
            } else {
                this.showMessage('predictionMessage', '加载模型失败，请检查服务器状态', 'error');
            }
        } catch (error) {
            console.error('加载模型失败:', error);
            this.showMessage('predictionMessage', '无法连接到服务器，请检查网络连接', 'error');
        }
    }

    // 填充模型选择下拉框
    populateModelSelect() {
        const select = document.getElementById('modelSelect');
        select.innerHTML = '<option value="">请选择模型...</option>';
        
        this.availableModels.forEach(model => {
            const option = document.createElement('option');
            option.value = model.model_key;
            option.textContent = `${model.model_type.toUpperCase()} - ${this.getVariantName(model.variant)}`;
            select.appendChild(option);
        });
    }

    // 获取变体中文名称
    getVariantName(variant) {
        const names = {
            'original': '原始',
            'pruned': '剪枝',
            'distilled': '蒸馏',
            'quantized': '量化'
        };
        return names[variant] || variant;
    }

    // 处理图像选择
    handleImageSelect(file) {
        if (!file) return;

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('predictionMessage', '请选择有效的图像文件', 'error');
            return;
        }

        // 验证文件大小 (10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showMessage('predictionMessage', '文件大小不能超过 10MB', 'error');
            return;
        }

        this.selectedFile = file;
        this.showImagePreview(file);
        this.updatePredictButton();
        this.clearMessage('predictionMessage');
    }

    // 显示图像预览
    showImagePreview(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const preview = document.getElementById('imagePreview');
            const img = document.getElementById('previewImage');
            img.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }

    // 更新预测按钮状态
    updatePredictButton() {
        const button = document.getElementById('predictButton');
        const modelSelected = document.getElementById('modelSelect').value;
        const fileSelected = this.selectedFile;

        button.disabled = !modelSelected || !fileSelected;
    }

    // 执行预测
    async performPrediction() {
        if (!this.selectedFile) {
            this.showMessage('predictionMessage', '请先选择图像文件', 'error');
            return;
        }

        const modelKey = document.getElementById('modelSelect').value;
        if (!modelKey) {
            this.showMessage('predictionMessage', '请先选择模型', 'error');
            return;
        }

        // 显示加载状态
        this.showLoading('predictionLoading');
        this.hideElement('predictionResults');
        this.hideElement('inferenceTime');
        this.clearMessage('predictionMessage');

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('model_key', modelKey);

            const response = await fetch(`${this.apiBase}/predict/`, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.displayPredictionResults(data.results, data.inference_time);
                this.showMessage('predictionMessage', '预测完成', 'success');
            } else {
                this.showMessage('predictionMessage', `预测失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('预测失败:', error);
            this.showMessage('predictionMessage', '预测请求失败，请检查网络连接', 'error');
        } finally {
            this.hideLoading('predictionLoading');
        }
    }

    // 显示预测结果
    displayPredictionResults(results, inferenceTime) {
        const resultsContainer = document.getElementById('resultsContent');
        const resultsSection = document.getElementById('predictionResults');
        const timeSection = document.getElementById('inferenceTime');

        // 清空之前的结果
        resultsContainer.innerHTML = '';

        // 按置信度排序
        const sortedResults = Object.entries(results)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5); // 只显示前5个结果

        // 创建结果项
        sortedResults.forEach(([className, confidence]) => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';

            resultItem.innerHTML = `
                <span class="result-label">${this.formatClassName(className)}</span>
                <div style="display: flex; align-items: center;">
                    <span class="result-confidence">${(confidence * 100).toFixed(2)}%</span>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${confidence * 100}%"></div>
                    </div>
                </div>
            `;

            resultsContainer.appendChild(resultItem);
        });

        // 显示结果和推理时间
        resultsSection.style.display = 'block';
        if (inferenceTime) {
            timeSection.textContent = inferenceTime;
            timeSection.style.display = 'block';
        }
    }

    // 格式化类别名称
    formatClassName(className) {
        return className.replace(/_/g, ' ')
                       .split(' ')
                       .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                       .join(' ');
    }

    // 执行模型比较
    async performComparison() {
        const modelType = document.getElementById('modelTypeSelect').value;
        const comparisonType = document.getElementById('comparisonTypeSelect').value;

        // 验证量化比较只支持ResNet50
        if (comparisonType === 'quantization' && modelType !== 'resnet50') {
            this.showMessage('comparisonMessage', '量化比较仅支持 ResNet50 模型', 'error');
            return;
        }

        // 显示加载状态
        this.showLoading('comparisonLoading');
        this.clearMessage('comparisonMessage');

        try {
            const response = await fetch(`${this.apiBase}/models/${modelType}/comparison/${comparisonType}`);
            const data = await response.json();

            if (data.success) {
                this.displayComparisonResults(data.html_content);
                this.showMessage('comparisonMessage', '比较报告生成成功', 'success');
            } else {
                this.showMessage('comparisonMessage', `生成失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('比较失败:', error);
            this.showMessage('comparisonMessage', '比较请求失败，请检查网络连接', 'error');
        } finally {
            this.hideLoading('comparisonLoading');
        }
    }

    // 显示比较结果
    displayComparisonResults(htmlContent) {
        const resultsContainer = document.getElementById('comparisonResults');
        resultsContainer.innerHTML = htmlContent;
    }

    // 更新量化可用性
    updateQuantizationAvailability() {
        const modelType = document.getElementById('modelTypeSelect').value;
        const comparisonType = document.getElementById('comparisonTypeSelect').value;
        const compareButton = document.getElementById('compareButton');

        if (comparisonType === 'quantization' && modelType !== 'resnet50') {
            compareButton.disabled = true;
            compareButton.textContent = '量化比较仅支持 ResNet50';
        } else {
            compareButton.disabled = false;
            compareButton.textContent = '生成比较报告';
        }
    }

    // 显示消息
    showMessage(elementId, message, type = 'info') {
        const element = document.getElementById(elementId);
        element.innerHTML = `<div class="message ${type}">${message}</div>`;
    }

    // 清除消息
    clearMessage(elementId) {
        const element = document.getElementById(elementId);
        element.innerHTML = '';
    }

    // 显示加载状态
    showLoading(elementId) {
        const element = document.getElementById(elementId);
        element.style.display = 'block';
    }

    // 隐藏加载状态
    hideLoading(elementId) {
        const element = document.getElementById(elementId);
        element.style.display = 'none';
    }

    // 隐藏元素
    hideElement(elementId) {
        const element = document.getElementById(elementId);
        element.style.display = 'none';
    }
}

// 标签页切换功能
function switchTab(tabName) {
    // 隐藏所有标签页内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    // 移除所有标签按钮的活动状态
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('active');
    });

    // 显示选中的标签页内容
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // 激活对应的标签按钮
    const activeButton = event.target;
    activeButton.classList.add('active');
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new RemoteSensingApp();

    // 初始化量化可用性检查
    setTimeout(() => {
        if (window.app.updateQuantizationAvailability) {
            window.app.updateQuantizationAvailability();
        }
    }, 100);
});
