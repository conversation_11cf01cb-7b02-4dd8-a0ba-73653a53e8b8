import os
from typing import List


class Settings:
    """
    应用配置类
    
    从环境变量读取配置，提供默认值
    """
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    
    # 模型配置
    MODEL_BASE_DIR: str = os.getenv("MODEL_BASE_DIR", "outputs")
    DEVICE: str = os.getenv("DEVICE", "auto")
    
    # 图像上传配置
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "imgs")
    MAX_UPLOAD_SIZE: int = int(os.getenv("MAX_UPLOAD_SIZE", "10485760"))  # 10MB
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    # CORS配置
    CORS_ORIGINS: List[str] = os.getenv("CORS_ORIGINS", "*").split(",")
    
    # 模型缓存配置
    MODEL_CACHE_SIZE: int = int(os.getenv("MODEL_CACHE_SIZE", "1"))
    
    # API配置
    API_PREFIX: str = os.getenv("API_PREFIX", "/api")
    API_TITLE: str = os.getenv("API_TITLE", "遥感图像分类API")
    API_VERSION: str = os.getenv("API_VERSION", "1.0.0")
    API_DESCRIPTION: str = os.getenv("API_DESCRIPTION", "基于深度学习的遥感场景分类系统API")
    
    @classmethod
    def get_device(cls):
        """
        获取计算设备
        
        Returns:
            设备字符串
        """
        if cls.DEVICE.lower() == "auto":
            import torch
            return "cuda" if torch.cuda.is_available() else "cpu"
        return cls.DEVICE.lower()


# 全局配置实例
settings = Settings()
