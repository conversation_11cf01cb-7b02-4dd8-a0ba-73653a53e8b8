#!/bin/bash

# Docker构建和部署脚本
# 用法: ./docker-build.sh [选项]

set -e

# 默认配置
IMAGE_NAME="remote-sensing-api"
IMAGE_TAG="latest"
BUILD_CONTEXT="."
DOCKERFILE="Dockerfile"
PUSH_TO_REGISTRY=false
REGISTRY_URL=""
GPU_SUPPORT=false
DEV_MODE=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Docker构建和部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -t, --tag TAG           设置镜像标签 (默认: latest)"
    echo "  -n, --name NAME         设置镜像名称 (默认: remote-sensing-api)"
    echo "  -p, --push              构建后推送到注册表"
    echo "  -r, --registry URL      设置注册表URL"
    echo "  -g, --gpu               启用GPU支持"
    echo "  -d, --dev               开发模式"
    echo "  --no-cache              不使用缓存构建"
    echo ""
    echo "示例:"
    echo "  $0                      # 基本构建"
    echo "  $0 -t v1.0.0           # 指定标签构建"
    echo "  $0 -g                   # GPU支持构建"
    echo "  $0 -d                   # 开发模式构建"
    echo "  $0 -p -r registry.com   # 构建并推送到注册表"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -r|--registry)
            REGISTRY_URL="$2"
            shift 2
            ;;
        -g|--gpu)
            GPU_SUPPORT=true
            shift
            ;;
        -d|--dev)
            DEV_MODE=true
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        *)
            print_message $RED "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 构建完整镜像名称
if [[ -n "$REGISTRY_URL" ]]; then
    FULL_IMAGE_NAME="${REGISTRY_URL}/${IMAGE_NAME}:${IMAGE_TAG}"
else
    FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"
fi

# 显示构建信息
print_message $BLUE "=== Docker构建配置 ==="
echo "镜像名称: $FULL_IMAGE_NAME"
echo "构建上下文: $BUILD_CONTEXT"
echo "Dockerfile: $DOCKERFILE"
echo "GPU支持: $GPU_SUPPORT"
echo "开发模式: $DEV_MODE"
echo "推送到注册表: $PUSH_TO_REGISTRY"
echo ""

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    print_message $RED "错误: Docker未安装或不可用"
    exit 1
fi

# 检查Dockerfile是否存在
if [[ ! -f "$DOCKERFILE" ]]; then
    print_message $RED "错误: Dockerfile不存在: $DOCKERFILE"
    exit 1
fi

# 开始构建
print_message $YELLOW "开始构建Docker镜像..."

# 构建命令
BUILD_CMD="docker build $NO_CACHE -t $FULL_IMAGE_NAME -f $DOCKERFILE $BUILD_CONTEXT"

if [[ "$GPU_SUPPORT" == true ]]; then
    print_message $BLUE "启用GPU支持构建"
fi

print_message $BLUE "执行构建命令: $BUILD_CMD"
eval $BUILD_CMD

if [[ $? -eq 0 ]]; then
    print_message $GREEN "✅ 镜像构建成功: $FULL_IMAGE_NAME"
else
    print_message $RED "❌ 镜像构建失败"
    exit 1
fi

# 显示镜像信息
print_message $BLUE "镜像信息:"
docker images $FULL_IMAGE_NAME

# 推送到注册表
if [[ "$PUSH_TO_REGISTRY" == true ]]; then
    if [[ -z "$REGISTRY_URL" ]]; then
        print_message $RED "错误: 推送需要指定注册表URL (-r 选项)"
        exit 1
    fi
    
    print_message $YELLOW "推送镜像到注册表..."
    docker push $FULL_IMAGE_NAME
    
    if [[ $? -eq 0 ]]; then
        print_message $GREEN "✅ 镜像推送成功"
    else
        print_message $RED "❌ 镜像推送失败"
        exit 1
    fi
fi

# 提供运行建议
print_message $BLUE "=== 运行建议 ==="
echo "使用Docker运行:"
echo "  docker run -p 8000:8000 $FULL_IMAGE_NAME"
echo ""
echo "使用Docker Compose运行:"
if [[ "$GPU_SUPPORT" == true ]]; then
    echo "  docker-compose -f docker-compose.gpu.yml up"
elif [[ "$DEV_MODE" == true ]]; then
    echo "  docker-compose --profile dev up remote-sensing-dev"
else
    echo "  docker-compose up"
fi
echo ""
echo "访问应用:"
echo "  http://localhost:8000"
echo "  http://localhost:8000/docs (API文档)"

print_message $GREEN "🎉 构建完成!"
