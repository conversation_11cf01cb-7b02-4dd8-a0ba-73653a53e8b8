# 部署指南

本文档提供遥感图像分类系统的完整部署指南，涵盖本地开发、Docker 部署和生产环境部署。

## 📋 目录

- [系统要求](#系统要求)
- [本地开发环境](#本地开发环境)
- [Docker 部署](#docker部署)
- [生产环境部署](#生产环境部署)
- [环境变量配置](#环境变量配置)
- [性能优化](#性能优化)
- [监控和维护](#监控和维护)

## 🔧 系统要求

### 最低要求

- **操作系统**: Linux (Ubuntu 18.04+), macOS (10.14+), Windows 10+
- **Python**: 3.8+ (推荐 3.11)
- **内存**: 4GB RAM (推荐 8GB+)
- **存储**: 10GB 可用空间
- **网络**: 互联网连接（用于下载依赖和模型）

### 推荐配置

- **CPU**: 4 核心以上
- **内存**: 16GB+ RAM
- **GPU**: NVIDIA GPU (CUDA 11.0+) 用于加速推理
- **存储**: SSD 硬盘，20GB+ 可用空间

### GPU 支持（可选）

如需 GPU 加速，请确保：

- NVIDIA GPU (计算能力 3.5+)
- NVIDIA 驱动 (450.80.02+)
- CUDA Toolkit (11.0+)
- cuDNN (8.0+)

## 🏠 本地开发环境

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd yaogan

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 升级pip
pip install --upgrade pip
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 验证安装
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

基本配置示例：

```bash
# .env
HOST=127.0.0.1
PORT=8000
DEBUG=true
DEVICE=auto
MODEL_BASE_DIR=outputs
LOG_LEVEL=DEBUG
```

### 4. 启动开发服务器

```bash
# 方式一：使用启动脚本
python run_api.py

# 方式二：直接使用uvicorn
uvicorn src.api.main:app --host 127.0.0.1 --port 8000 --reload

# 方式三：使用Makefile
make dev
```

### 5. 验证部署

访问以下地址验证部署：

- 主页: http://localhost:8000
- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 🐳 Docker 部署

### 1. 安装 Docker

**Ubuntu/Debian:**

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 添加用户到docker组
sudo usermod -aG docker $USER
newgrp docker

# 验证安装
docker --version
```

**macOS/Windows:**
下载并安装 [Docker Desktop](https://www.docker.com/products/docker-desktop)

### 2. 基本 Docker 部署

```bash
# 构建镜像
make build
# 或
docker build -t remote-sensing-api .

# 运行容器
make run
# 或
docker run -p 8000:8000 remote-sensing-api
```

### 3. 使用 Docker Compose

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 4. GPU 支持部署

**安装 NVIDIA Docker:**

```bash
# Ubuntu
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

**GPU 模式部署:**

```bash
# 构建GPU支持镜像
make gpu-build

# 运行GPU容器
make gpu-run

# 或使用Docker Compose
docker-compose -f docker-compose.gpu.yml up -d
```

## 🏭 生产环境部署

### 1. 服务器准备

**系统配置:**

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git htop

# 配置防火墙
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 8000  # 应用端口
sudo ufw enable
```

### 2. 反向代理配置

**使用 Nginx:**

```nginx
# /etc/nginx/sites-available/remote-sensing-api
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 文件上传大小限制
        client_max_body_size 10M;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件直接服务
    location /static/ {
        alias /path/to/app/src/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

**启用配置:**

```bash
sudo ln -s /etc/nginx/sites-available/remote-sensing-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. SSL 证书配置

**使用 Let's Encrypt:**

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 4. 系统服务配置

**创建 systemd 服务:**

```ini
# /etc/systemd/system/remote-sensing-api.service
[Unit]
Description=Remote Sensing API
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/remote-sensing-api
Environment=PATH=/opt/remote-sensing-api/venv/bin
ExecStart=/opt/remote-sensing-api/venv/bin/python run_api.py
Restart=always
RestartSec=10

# 环境变量
Environment=HOST=127.0.0.1
Environment=PORT=8000
Environment=DEBUG=false
Environment=LOG_LEVEL=INFO

[Install]
WantedBy=multi-user.target
```

**启用服务:**

```bash
sudo systemctl daemon-reload
sudo systemctl enable remote-sensing-api
sudo systemctl start remote-sensing-api
sudo systemctl status remote-sensing-api
```

## ⚙️ 环境变量配置

### 核心配置变量

| 变量名           | 默认值    | 描述           | 示例               |
| ---------------- | --------- | -------------- | ------------------ |
| `HOST`           | `0.0.0.0` | 服务器绑定地址 | `127.0.0.1`        |
| `PORT`           | `8000`    | 服务器端口     | `8080`             |
| `DEBUG`          | `false`   | 调试模式       | `true`             |
| `DEVICE`         | `auto`    | 计算设备       | `cuda`, `cpu`      |
| `MODEL_BASE_DIR` | `outputs` | 模型基础目录   | `/data/models`     |
| `LOG_LEVEL`      | `INFO`    | 日志级别       | `DEBUG`, `WARNING` |

### 高级配置变量

| 变量名             | 默认值     | 描述               |
| ------------------ | ---------- | ------------------ |
| `UPLOAD_DIR`       | `imgs`     | 上传文件目录       |
| `MAX_UPLOAD_SIZE`  | `10485760` | 最大上传大小(字节) |
| `CORS_ORIGINS`     | `*`        | 允许的跨域来源     |
| `MODEL_CACHE_SIZE` | `1`        | 模型缓存数量       |
| `API_PREFIX`       | `/api`     | API 路径前缀       |

### 配置示例

**开发环境 (.env.dev):**

```bash
HOST=127.0.0.1
PORT=8000
DEBUG=true
DEVICE=auto
LOG_LEVEL=DEBUG
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
```

**生产环境 (.env.prod):**

```bash
HOST=0.0.0.0
PORT=8000
DEBUG=false
DEVICE=cuda
LOG_LEVEL=INFO
CORS_ORIGINS=https://yourdomain.com
MAX_UPLOAD_SIZE=20971520
MODEL_CACHE_SIZE=2
```

## 🚀 性能优化

### 1. 应用层优化

**模型缓存优化:**

```bash
# 增加模型缓存数量
MODEL_CACHE_SIZE=3

# 预加载常用模型
# 在启动脚本中添加预热请求
```

**并发配置:**

```bash
# 使用多worker模式
uvicorn src.api.main:app --workers 4 --host 0.0.0.0 --port 8000
```

### 2. 系统层优化

**内存优化:**

```bash
# 设置swap
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

**文件描述符限制:**

```bash
# /etc/security/limits.conf
* soft nofile 65536
* hard nofile 65536
```

### 3. 网络优化

**Nginx 优化:**

```nginx
# 启用gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

# 连接池优化
upstream app_servers {
    server 127.0.0.1:8000;
    keepalive 32;
}
```

### 4. GPU 优化

**CUDA 内存管理:**

```python
# 在应用启动时设置
import torch
torch.cuda.empty_cache()
torch.backends.cudnn.benchmark = True
```

## 📊 监控和维护

### 1. 健康检查

**应用健康检查:**

```bash
# 手动检查
curl http://localhost:8000/health

# 自动监控脚本
#!/bin/bash
while true; do
    if ! curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "$(date): Health check failed" >> /var/log/app-monitor.log
        # 重启服务或发送告警
    fi
    sleep 30
done
```

### 2. 日志管理

**日志轮转配置:**

```bash
# /etc/logrotate.d/remote-sensing-api
/var/log/remote-sensing-api/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload remote-sensing-api
    endscript
}
```

### 3. 性能监控

**系统监控:**

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控脚本
#!/bin/bash
echo "=== $(date) ===" >> /var/log/performance.log
echo "CPU Usage:" >> /var/log/performance.log
top -bn1 | grep "Cpu(s)" >> /var/log/performance.log
echo "Memory Usage:" >> /var/log/performance.log
free -h >> /var/log/performance.log
echo "Disk Usage:" >> /var/log/performance.log
df -h >> /var/log/performance.log
```

### 4. 备份策略

**数据备份:**

```bash
#!/bin/bash
# 备份脚本
BACKUP_DIR="/backup/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份模型文件
tar -czf $BACKUP_DIR/models.tar.gz outputs/

# 备份配置文件
cp .env $BACKUP_DIR/
cp docker-compose.yml $BACKUP_DIR/

# 清理旧备份（保留30天）
find /backup -type d -mtime +30 -exec rm -rf {} \;
```

### 5. 故障恢复

**自动重启配置:**

```bash
# systemd服务自动重启
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3
```

**容器自动重启:**

```yaml
# docker-compose.yml
restart: unless-stopped
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

## 🔒 安全配置

### 1. 防火墙配置

```bash
# 基本防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. 应用安全

**CORS 配置:**

```bash
# 生产环境限制CORS
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com
```

**文件上传安全:**

```bash
# 限制上传文件大小和类型
MAX_UPLOAD_SIZE=10485760  # 10MB
# 在代码中验证文件类型
```

### 3. 容器安全

**非 root 用户运行:**

```dockerfile
# Dockerfile中已配置
USER appuser
```

**资源限制:**

```yaml
# docker-compose.yml
deploy:
  resources:
    limits:
      memory: 4G
      cpus: "2.0"
```

## 📚 常用命令参考

### Docker 命令

```bash
# 构建和运行
make build && make run

# 查看日志
make logs

# 进入容器
make shell

# 清理资源
make clean

# 健康检查
make health
```

### 系统服务命令

```bash
# 服务管理
sudo systemctl start remote-sensing-api
sudo systemctl stop remote-sensing-api
sudo systemctl restart remote-sensing-api
sudo systemctl status remote-sensing-api

# 查看日志
sudo journalctl -u remote-sensing-api -f
```

### 监控命令

```bash
# 系统资源
htop
iotop
nethogs

# 应用状态
curl http://localhost:8000/health
curl http://localhost:8000/api/info

# Docker状态
docker ps
docker stats
```
