version: '3.8'

services:
  # GPU支持的主应用服务
  remote-sensing-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: remote-sensing-api-gpu
    ports:
      - "8000:8000"
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - MODEL_BASE_DIR=outputs
      - DEVICE=cuda
      - UPLOAD_DIR=imgs
      - MAX_UPLOAD_SIZE=10485760
      - LOG_LEVEL=INFO
      - CORS_ORIGINS=*
      - MODEL_CACHE_SIZE=1
      - API_PREFIX=/api
      - API_TITLE=遥感图像分类API
      - API_VERSION=1.0.0
      - API_DESCRIPTION=基于深度学习的遥感场景分类系统API
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
    volumes:
      # 挂载模型输出目录
      - ./outputs:/app/outputs:ro
      # 挂载图像上传目录
      - ./imgs:/app/imgs
      # 挂载日志目录
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 8G
        reservations:
          memory: 4G
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    # GPU运行时支持
    runtime: nvidia

# 网络配置
networks:
  default:
    name: remote-sensing-gpu-network

# 卷配置
volumes:
  model_cache:
    driver: local
  upload_cache:
    driver: local
